// import { useSession } from 'next-auth/react';
import React, { useState, useEffect, useContext } from "react";
import { useRouter } from 'next/router'
import Head from 'next/head'
import { signIn, signOut, useSession } from 'next-auth/react'
import { appvars } from '../../lib/constants'
import { Box, Button, Container, Card, CardHeader, Collapse, Stack, Typography, Tooltip, Chip, CircularProgress, IconButton, TextField, Checkbox, Select, MenuItem } from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import AppLayout from '../../lib/layouts/layout.user'
import { ColorModeContext } from "../../lib/context/Provider.themeDarkMode";
import _ from 'lodash';

import CloseIcon from '@mui/icons-material/Close';
import ListItemText from '@mui/material/ListItemText';
import Dialog from '@mui/material/Dialog';
import Autocomplete from '@mui/material/Autocomplete';
import LinearProgress from '@mui/material/LinearProgress';
import Link from 'next/link'

import { styled } from '@mui/material/styles';
import { FormControl, createTheme } from '@mui/material';

function useStickyState(defaultValue, key) {
    const [value, setValue] = React.useState(defaultValue);
    // console.log('useStickyState', defaultValue, key)
    React.useEffect(() => {
        const stickyValue = window.localStorage.getItem(key);
        if (stickyValue && stickyValue !== 'null') {
            // console.log('stickyValue2', JSON.parse(stickyValue))
            try { setValue(JSON.parse(stickyValue)); } catch (e) {
                console.log('battle pairs error stickyValue', stickyValue, e)
            }
        } else {
            const fData = async () => {
                // console.log('fData call binance')
                var dt = await BinanceFuturesDaily();
                // console.log('fData JSON.stringify(data)',  JSON.stringify(dt.data))
                // window.localStorage.setItem(key, JSON.stringify(dt));
                setValue(JSON.parse(JSON.stringify(dt)));
            }
            fData();
        }
    }, [key]);

    React.useEffect(() => {
        // window.localStorage.setItem(key, JSON.stringify(value));
    }, [key, value]);

    return [value, setValue];
}
export default function Home(props) {
    const { ...rest } = props;
    const router = useRouter();
    const { mode, colorMode, drawerCollapsed, drawerToggled, drawerBroken } = useContext(ColorModeContext);
    const { slug } = router.query
    const [header, setHeader] = useState(false)
    const { status, data: session } = useSession({
        required: true,
        onUnauthenticated() {
            signIn();
            // router.push('/auth/SignInSide');
        },
    });
    const { user } = session ? session : {};
    const { token, refreshToken } = user ? user : {};
    useEffect(() => {
        colorMode.setCurrPage('/ai/market-anomali');
        drawerCollapsed && colorMode.collapseDrawer(true);
    }, [])

    const [battleParams, setbattleParamsFN] = React.useState({
        intervals: ["1m", "1h", "1d"],
        battleInterval: ["1m"],
        candleCounts: 24,
    });
    const [sbattleParams, ssetbattleParamsFN] = useStickyState(BattleDefaultParameters, 'backtestParameters');
    const [isPreparingData, setIsPreparingData] = React.useState(false);
    const [isFormOK, setIsFormOK] = React.useState(false);
    const [isDataUpdated, setIsDataUpdated] = React.useState(false);
    const [expanded, setExpanded] = React.useState(true);
    const [anomalyData, setAnomalyData] = useState(false);
    const [aiResponse, setAiResponse] = useState(null);
    const handleExpandClick = () => {
        setExpanded(!expanded);
    };
    useEffect(() => {
        anomalyData && console.log('anomalyData', anomalyData)
    }, [anomalyData])
    const showLocalcache = () => {
        let varx = window.localStorage.getItem('aiAnomalyParameters');
        // console.log('showLocalcache', varx)
    }

    const handleResetAll = async () => {
        try {
            // Confirm reset action
            if (!confirm('Tüm veriler sıfırlanacak ve uygulama başlangıç durumuna dönecek. Devam etmek istiyor musunuz?')) {
                return;
            }

            setIsPreparingData(true);

            // 1. API endpoint'ini çağır - SQLite tabloları temizle
            const resetResponse = await fetch('/api/pub/data/resetklines', {
                method: 'POST',
                headers: {
                    'Authorization': 'Bearer ' + props.token,
                    'X-Host': 'Subanet.com',
                },
                body: JSON.stringify({})
            });

            if (!resetResponse.ok) {
                throw new Error(`Reset API error: ${resetResponse.status} - ${resetResponse.statusText}`);
            }

            const resetData = await resetResponse.json();
            console.log('Reset API response:', resetData);

            // 2. Tüm localStorage verilerini temizle
            const keysToRemove = [
                'backtestParameters',
                'bPairs',
                'anomalyList',
                'aiAnomalyParameters'
            ];

            keysToRemove.forEach(key => {
                window.localStorage.removeItem(key);
            });

            // 3. Tüm state'leri başlangıç değerlerine sıfırla
            setbattleParamsFN({
                intervals: ["1m", "1h", "1d"],
                battleInterval: ["1m"],
                candleCounts: 24,
            });

            ssetbattleParamsFN(BattleDefaultParameters);
            setIsFormOK(false);
            setIsDataUpdated(false);
            setExpanded(true);
            setAnomalyData(false);
            setAiResponse(null);

            setIsPreparingData(false);

            alert('Tüm veriler başarıyla sıfırlandı. Sayfa yeniden yüklenecek.');

            // 4. Sayfayı yenile
            window.location.reload();

        } catch (error) {
            console.error('Reset error:', error);
            alert('Reset işlemi sırasında hata oluştu: ' + error.message);
            setIsPreparingData(false);
        }
    }
    const clearCache = () => {
        window.localStorage.setItem('aiAnomalyParameters', null);
        setbattleParamsFN({})
    }
    const save2Sandbox = async (dataName, dataValue) => {
        let uri = '/api/pub/ai/sandbox_savedata'
        try {
            const res = await fetch(uri, {
                method: 'POST',
                headers: {
                    'Authorization': 'Bearer ' + props.token,
                    'X-Host': 'Subanet.com',
                },
                body: JSON.stringify({ dataName: dataName || 'params', dataValue: dataValue || battleParams }),
            })
            if (!res.ok) {
                var message = `An error has occured: ${res.status} - ${res.statusText}`;
                alert(message);
                return
            }

            const datax = await res.json();

            if (!datax.error) {
                return true
            } else {
                console.log('err desc', datax);
                alert('action failed! \n'); //JSON.stringify(values, null, 2)
            }
        }
        catch (e) {
            console.log('e', e)
            alert('Error Code: 981', e)
        }
    }
    const prepAnomaliData = async () => {
        setIsPreparingData(true);
        try {
            let resp = await save2Sandbox('params', battleParams);
            if (resp) {
                let uri = '/api/pub/ai/prepare_anomaly_data'
                try {
                    const dtBOP = Date.now();
                    console.log('BOP', new Date(dtBOP).toISOString());
                    const res = await fetch(uri, {
                        method: 'POST',
                        headers: {
                            'Authorization': 'Bearer ' + props.token,
                            'X-Host': 'Subanet.com',
                        },
                        body: JSON.stringify({ dataName: 'params', dataValue: battleParams }),
                    })
                    if (!res.ok) {
                        var message = `An error has occured: ${res.status} - ${res.statusText}`;
                        alert(message);
                        return
                    }

                    const datax = await res.json();

                    if (!datax.error) {
                        setIsDataUpdated(true);
                        console.log('datax', datax)
                        console.log('EOP', new Date().toISOString(), Date.now(), 'elapsed', Date.now() - dtBOP);
                        alert('data prepared!')
                    } else {
                        console.log('err desc', datax);
                        alert('action failed! \n'); //JSON.stringify(values, null, 2)
                    }
                }
                catch (e) {
                    console.log('e', e)
                    alert('Error Code: 981', e)
                }
            }
        } finally {
            setIsPreparingData(false);
        }
    }
    const setParameters = async val => {
        let currV = JSON.parse(JSON.stringify(battleParams));
        let nVal = {
            ...currV,
        };
        let { fname, fvar } = val;
        nVal[fname] = fvar;
        // console.log('set backtestParams', val, battleParams, nVal)
        setbattleParamsFN(nVal);
        ssetbattleParamsFN(nVal);
        await save2Sandbox('params', nVal);
        fname == 'pairs' && await save2Sandbox('pairs', fvar);
        // alert('data saved!')
        // props.setbattleParams && props.setbattleParams(vals);
        let isFormOK = checkForm(nVal);
        setIsFormOK(isFormOK);
    }
    const checkForm = (vals) => {
        let isOK = true;
        // console.log('vals', vals);
        if (!vals.pairs || vals.pairs.length == 0) {
            isOK = false;
        };
        // console.log('!vals.pairs || vals.pairs.length == 0', !vals.pairs || vals.pairs.length == 0);

        if (!vals.intervals || vals.intervals.length == 0) {
            isOK = false;
        };
        // console.log('!vals.intervals || vals.intervals.length == 0', !vals.intervals || vals.intervals.length == 0);

        if (!vals.battleInterval || vals.battleInterval.length == 0) {
            isOK = false;
        };
        // console.log('!vals.battleInterval || vals.battleInterval.length == 0', !vals.battleInterval || vals.battleInterval.length == 0);

        //battleinterval değer intervals içinde var mı?
        if ((!Array.isArray(vals.intervals)) ||
            (Array.isArray(vals.intervals) &&
                (!Array.isArray(vals.battleInterval) || vals.battleInterval.length === 0 ||
                    !vals.intervals.includes(vals.battleInterval[0])))) {
            isOK = false;
        };
        // console.log('(!Array.isArray(vals.intervals))', (!Array.isArray(vals.intervals)));
        // console.log('!vals.intervals.includes(vals.battleInterval)', Array.isArray(vals.intervals) && !vals.intervals.includes(vals.battleInterval));
        !isOK && console.log('isOK', isOK);
        return isOK;
    }
    useEffect(() => {
        const getList = async () => {
            showLocalcache();
            //   setbattleParamsFN(sbattleParams);
        }
        getList();
    }, []);

    if (status === "loading") {
        return "Loading..."
    }
    return (
        <>
            <Head>
                <title>Gauss Algo - Nodes</title>
            </Head>
            <AppLayout session={session} {...props}
                pgTitle={header ? header : "" + ((Array.isArray(slug) && slug[0]) ? ('User / ' + slug[0].toString().substring(0, 15) + '...') : 'User Management')}
                pgBread={<span>x</span>}
            >   
                <Box sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center', justifyContent: 'flex-start', mb: 0 }}>
                    <Typography variant="h5" sx={{ px: 4, pt: 2, }}>AI Market Anomaly Detector</Typography>
                    <Chip key={'localLLMLoading'}
                        size='small'
                        style={{ border: '1px solid #ccc', marginRight: 5 }}
                        sx={{ borderRadius: 2, color: '#000' }}
                        label={'Reset Parameters'}
                        onClick={handleResetAll}
                        disabled={false}
                    />
                    <Chip key={'testResponse'}
                        size='small'
                        style={{ border: '1px solid #ccc', marginRight: 5 }}
                        sx={{ borderRadius: 2, backgroundColor: '#4CAF50', color: '#fff' }}
                        label={'Test Response'}
                        onClick={() => {
                            const testResponse = `{
  "analysisTimestamp": "2025-08-23T21:56:54.707Z",
  "analysisLastBarTimestamp": "2025-08-23T21:56:00.000Z",
  "marketAnalysis": {
    "overallCondition": "Piyasa, belirgin bir volatiliteye ve işlem hacminde ani artışlara sahip, risk iştahının orta düzeyde olduğu bir dönemde.",
    "observation": "Özellikle yeni listelenmiş veya son dönemde dikkat çeken altcoin pariteleri, genel piyasa trendlerinden ayrışarak yüksek volatilite ve hacim anomalileri sergilemektedir."
  },
  "recommendedPairs": [
    {
      "symbol": "WLFIUSDT",
      "confidenceScore": 92,
      "analysis": {
        "marketAnomaly": "Piyasa geneline kıyasla olağanüstü yüksek fiyat değişim yüzdesi (%63.83) ve geniş fiyat aralığı (%price_spread_percent: 106.19). Bu, güçlü bir momentum ve işlem iştahına işaret ediyor.",
        "internalAnomaly": "Yüksek gün içi volatilite ve fiyatın %36.69'luk diliminde olması, mevcut trendin devam edebileceği sinyali veriyor."
      }
    },
    {
      "symbol": "PROMPTUSDT",
      "confidenceScore": 88,
      "analysis": {
        "marketAnomaly": "Son derece yüksek fiyat değişim yüzdesi (%115.91) ve fiyatın 24 saatlik aralığın %89.24'ünde olması, güçlü bir yükseliş momentumuna işaret ediyor.",
        "internalAnomaly": "Son 4 saatlik kline verileri, fiyatın önemli bir yükseliş eğiliminde olduğunu ve yüksek hacimle desteklendiğini gösteriyor."
      }
    }
  ],
  "strategies": [
    {
      "symbol": "WLFIUSDT",
      "direction": "Long",
      "logic": "WLFIUSDT, son 24 saatteki güçlü fiyat artışı (%63.83) ve yüksek hacmiyle öne çıkıyor.",
      "entryLevels": [
        {
          "type": "Primary",
          "level": "0.3280000",
          "logic": "Mevcut fiyat seviyesinin hafif üzerinde, kısa vadeli direnç kırılımı beklentisiyle."
        },
        {
          "type": "Secondary",
          "level": "0.3250000",
          "logic": "Daha düşük bir giriş seviyesi için, olası geri çekilmelerde ortalama maliyeti düşürmek amacıyla."
        }
      ],
      "timeframe": {
        "tradeDuration": "15",
        "recommendationValidity": "30"
      },
      "riskManagement": {
        "stopLoss": {
          "level": "0.3200000",
          "logic": "Giriş seviyesinin altında, olası bir trend dönüşü veya piyasa düzeltmesi durumunda zararı sınırlamak için."
        }
      },
      "takeProfitLevels": [
        {
          "level": "0.3370000",
          "riskRewardRatio": "1:1.5",
          "logic": "Giriş seviyesinden %3'lük bir artış hedefi."
        },
        {
          "level": "0.3450000",
          "riskRewardRatio": "1:2.5",
          "logic": "Daha agresif bir hedef seviyesi, güçlü momentum durumunda kârı maksimize etmek için."
        }
      ]
    }
  ]
}`;
                            setAiResponse(testResponse);
                        }}
                        disabled={false}
                    />
                </Box>
                <Card sx={{ mt: 1, border: 1, mx: 2, borderColor: '#ccc' }}>
                    <CardHeader
                        subheader="Set Parameters and Fetch Binance Data"
                        action={
                            <IconButton
                                onClick={handleExpandClick}
                                aria-expanded={expanded}
                                aria-label="show parameters"
                                sx={{
                                    transform: expanded ? 'rotate(180deg)' : 'rotate(0deg)',
                                    transition: 'transform 0.3s',
                                }}
                            >
                                <ExpandMoreIcon />
                            </IconButton>
                        }
                        onClick={handleExpandClick}
                        sx={{
                            background: '#efefef', borderRadius: 0,
                            padding: 0, mb: 0, px: 2, py: 1
                        }}
                    />
                    <Collapse in={expanded} timeout="auto" unmountOnExit>
                        <Stack className="mt-2 px-2">
                            {/* <Pairs {...props} user={user} token={token} /> */}

                            <Box sx={{ p: 2, }}>
                                <Stack sx={{ flexDirection: 'row', alignItems: 'flex-start', justifyContent: 'flex-start', mb: 0 }}>
                                    <Box sx={{ borderBottom: 1, borderColor: 'divider', minHeight: 35, minWidth: 150, width: 150 }}>
                                        <Typography sx={{ pl: 4 }} variant="button" display="block" gutterBottom>
                                            Pairs
                                        </Typography>
                                    </Box>

                                    <Box sx={{ borderBottom: 1, borderColor: 'divider', minHeight: 35, marginLeft: 2, width: '100%' }}>
                                        <Pairs {...props} user={user} token={token} callbackFN={setParameters} />

                                    </Box>
                                </Stack>
                            </Box>
                            <Box sx={{ p: 2, }}>
                                <Stack sx={{ flexDirection: 'row', alignItems: 'flex-start', justifyContent: 'flex-start', mb: 0 }}>
                                    <Box sx={{ borderBottom: 1, borderColor: 'divider', minHeight: 35, minWidth: 150, width: 150 }}>
                                        <Typography sx={{ pl: 4 }} variant="button" display="block" gutterBottom>
                                            Intervals
                                        </Typography>
                                    </Box>

                                    <Box sx={{ borderBottom: 1, borderColor: 'divider', minHeight: 35, marginLeft: 4, width: '100%' }}>
                                        <ParamTimes {...props} candleCounts={true} battleParams={battleParams} multi={true} callbackFN={setParameters} />
                                    </Box>
                                </Stack>
                            </Box>
                            {/* <Box sx={{ p: 2, }}>
                                <Stack sx={{ flexDirection: 'row', alignItems: 'flex-start', justifyContent: 'flex-start', mb: 0 }}>
                                    <Box sx={{ borderBottom: 1, borderColor: 'divider', minHeight: 35, minWidth: 150, width: 150 }}>
                                        <Typography sx={{ pl: 4 }} variant="button" display="block" gutterBottom>
                                            AI Interval
                                        </Typography>
                                    </Box>

                                    <Box sx={{ borderBottom: 1, borderColor: 'divider', minHeight: 35, marginLeft: 4, width: '100%' }}>
                                        <ParamTimes {...props} battleParams={battleParams} callbackFN={setParameters} />
                                    </Box>
                                </Stack>
                            </Box> */}
                            <Box sx={{ p: 2, display: 'flex', alignItems: 'center', justifyContent: 'flex-start', mb: 0 }}>
                                <Button
                                    variant="outlined"
                                    onClick={prepAnomaliData}
                                    disabled={isPreparingData || !isFormOK}
                                    startIcon={isPreparingData ? <CircularProgress size={20} /> : null}
                                >
                                    {isPreparingData ? 'Preparing Data...' : 'Prepare Anomaly Data'}
                                </Button>
                            </Box>
                        </Stack>
                    </Collapse>
                </Card>
                <AnomalyList {...props} battleParams={battleParams} user={user} token={token} setAnomalyData={setAnomalyData} isDataUpdated={isDataUpdated} isPreparingData={isPreparingData} />
                <PromptCard {...props} battleParams={battleParams} anomalyData={anomalyData} aiResponse={aiResponse} setAiResponse={setAiResponse} user={user} token={token} setAnomalyData={setAnomalyData} isDataUpdated={isDataUpdated} isPreparingData={isPreparingData} />
                <CloudLLMResponse {...props} battleParams={battleParams} anomalyData={anomalyData} aiResponse={aiResponse} user={user} token={token} setAnomalyData={setAnomalyData} isDataUpdated={isDataUpdated} isPreparingData={isPreparingData} />


            </AppLayout>
        </>
    )
}
export async function getServerSideProps() {
    return { props: { appvars } }
}
const Pairs = props => {
    const { user = {} } = props;
    const { login } = user;
    const [futuresDaily, setFuturesDaily] = useState(false)
    const [userFavorites, setUserFavorites] = useState(false)
    const [selectedPairs, setselectedPairs] = useState([]);
    const [loading, setLoading] = useState(false)
    const [isOpened, setisOpened] = useState(true);
    const [lastpair, setlastpair] = useState(false);
    const [mode, setMode] = useStickyState(null, 'bPairs');

    const Favoriler = [
        "BTCUSDT",
        "ETHUSDT",
        "SOLUSDT",
        "DOGEUSDT",
        "XRPUSDT",
        "BNBUSDT",
        "AVAXUSDT",
        "ADAUSDT",
        "DYDXUSDT",
        "FILUSDT"
    ];
    const handleChange = (tag, checked) => {
        if (true) {
            setlastpair(tag);
            const nextSelectedTags = checked ? [...selectedPairs, tag] : selectedPairs.filter(t => t !== tag);
            // setselectedPairs([...new Set(nextSelectedTags)]);
            const newA = [...new Set(nextSelectedTags)];
            setselectedPairs(newA);
            mode && props.callbackFN({ fname: 'pairs', fvar: newA });
            mode && Array.isArray(newA) && setlastpair(newA.slice(-1).pop());
        } else {
            const nextSelectedTags = checked ? [tag] : selectedPairs.filter(t => t !== tag);
            setselectedPairs(nextSelectedTags);
        }
    }
    useEffect(() => {
        if (!mode) {
            setLoading(true)
        } else {
            setFuturesDaily(mode?.data);
            setLoading(false)
        }
    }, [mode])

    useEffect(() => {
        if (props.battleParams) {
            let parami = props.battleParams.pairs
            parami && !_.isEqual(parami, selectedPairs) && setselectedPairs(parami)
        } else {
            // console.log('no default pairs', props.battleParams);
            // props.callbackFN({ fname: 'pairs', fvar: ["BTCUSDT"] });
        }
    }, [props.battleParams])

    const refresh = async () => {
        setLoading(true)
        try {
            setFuturesDaily([]);
            var dt = await BinanceFuturesDaily();
            // console.log('dt', dt)
            var arr = []
            setFuturesDaily(dt?.data);
            setMode(dt);
            setLoading(false)
        } catch (e) {
            setLoading(false)
        }
    }

    const selectA = () => {
        setselectedPairs(Array.isArray(futuresDaily) ? futuresDaily.map(p => p.s) : [])

        mode && props.callbackFN({ fname: 'pairs', fvar: Array.isArray(futuresDaily) ? futuresDaily.map(p => p.s) : [] });
        mode && Array.isArray(futuresDaily) && setlastpair((Array.isArray(futuresDaily) ? futuresDaily.map(p => p.s) : []).slice(-1).pop());

    }
    const setRandomPairs = (n) => {
        var array = (Array.isArray(futuresDaily) ? futuresDaily.map(p => p.s) : [])
        const shuffled = array.sort(() => 0.5 - Math.random());
        let selected = shuffled.slice(0, n);
        setselectedPairs(selected);
        mode && props.callbackFN({ fname: 'pairs', fvar: selected });
        mode && Array.isArray(selected) && setlastpair(selected.slice(-1).pop());
    }
    const [minVolume, setMinVolume] = useState('')
    const fnSelectMinVolume = (vals) => {
        setMinVolume(vals?.target.value ? parseFloat(vals?.target.value) : '')
        // setMinVolume(Array.isArray(futuresDaily) ? futuresDaily.map(p => p.s) : [])
    }

    const fnSetMinVolumeFilter = () => {
        var array = (Array.isArray(futuresDaily) ? futuresDaily.filter(p => p.vn > parseFloat(minVolume) * 1000000).map(p => p.s) : [])
        setselectedPairs(array);
        mode && props.callbackFN({ fname: 'pairs', fvar: array });
        mode && Array.isArray(array) && setlastpair(array.slice(-1).pop());
    }

    const openlink = () => {
        var urlX = 'https://www.tradingview.com/chart?symbol=BINANCE%3A' + lastpair + 'PERP';
        console.log('', new Date().toISOString(), lastpair);
        lastpair && window.open(urlX, "_blank");
    }

    const savePairs2Sandbox = async () => {
        let uri = '/api/pub/ai/sandbox_savedata'
        try {
            const res = await fetch(uri, {
                method: 'POST',
                headers: {
                    'Authorization': 'Bearer ' + props.token,
                    'X-Host': 'Subanet.com',
                },
                body: JSON.stringify({ dataName: 'pairs', dataValue: selectedPairs }),
            })
            if (!res.ok) {
                var message = `An error has occured: ${res.status} - ${res.statusText}`;
                alert(message);
                return
            }

            const datax = await res.json();

            if (!datax.error) {
                alert('pairs saved!')
            } else {
                console.log('err desc', datax);
                alert('battle action failed! \n'); //JSON.stringify(values, null, 2)
            }
        }
        catch (e) {
            console.log('e', e)
            alert('Error Code: 981', e)
        }
    }

    return (
        <>
            <div style={{ width: '100%' }}>
                <Box
                    sx={{
                        mr: 4,
                        flex: 1,
                        flexGrow: 1,
                        flexDirection: 'row',
                        alignItems: 'flex-start',
                        justifyContent: 'flex-start',
                        pb: 1,
                        width: '100%',
                    }}>
                    <Chip key={'219151bc'}
                        size='small'
                        style={{ border: '1px solid #ccc', marginRight: 5 }}
                        sx={{ borderRadius: 2, backgroundColor: '#378CE7', color: '#fff' }}
                        label={'Tazele'}
                        onClick={() => refresh()}
                    />
                    {loading && <CircularProgress color="inherit" size={16} />}
                    {Array.isArray(futuresDaily) && futuresDaily.length !== 0 && (
                        <>

                            <Chip key={'219151b'}
                                size='small'
                                style={{ border: '1px solid #ccc', marginRight: 5 }}
                                sx={{ borderRadius: 2, backgroundColor: '#378CE7', color: '#fff' }}
                                label={loading ? 'yükleniyor..' : isOpened ? 'Gizle' : 'Göster'}
                                onClick={() => setisOpened(!isOpened)}
                            />

                            <Chip
                                size='small'
                                style={{ border: '1px solid #ccc', marginRight: 5 }}
                                sx={{ borderRadius: 2, backgroundColor: '#67C6E3' }}
                                label={'Favoriler'}
                                key={'1as'}
                                onClick={() => {
                                    setselectedPairs(Favoriler);

                                    mode && props.callbackFN({ fname: 'pairs', fvar: Favoriler });
                                    mode && Array.isArray(Favoriler) && setlastpair(Favoriler.slice(-1).pop());
                                }}
                            />

                            <Chip key={'1992'}
                                size='small'
                                style={{ border: '1px solid #ccc', marginRight: 5 }}
                                sx={{ borderRadius: 2, backgroundColor: '#67C6E3' }}
                                label={'Rx'}
                                onClick={() => {
                                    let px = ["ADAUSDT", "BNBUSDT", "SOLUSDT", "ETHUSDT", "FILUSDT", "XRPUSDT"]
                                    setselectedPairs(px);

                                    mode && props.callbackFN({ fname: 'pairs', fvar: px });
                                    mode && Array.isArray(px) && setlastpair(px.slice(-1).pop());
                                }
                                }
                            />

                            <Chip key={'19'}
                                size='small'
                                style={{ border: '1px solid #ccc', marginRight: 5 }}
                                sx={{ borderRadius: 2, backgroundColor: '#67C6E3' }}
                                label={'R2'}
                                onClick={() => setRandomPairs(2)}
                            />

                            <Chip key={'219'}
                                size='small'
                                style={{ border: '1px solid #ccc', marginRight: 5 }}
                                sx={{ borderRadius: 2, backgroundColor: '#67C6E3' }}
                                label={'R5'}
                                onClick={() => setRandomPairs(5)}
                            />

                            <Chip key={'21915'}
                                size='small'
                                style={{ border: '1px solid #ccc', marginRight: 5 }}
                                sx={{ borderRadius: 2, backgroundColor: '#67C6E3' }}
                                label={'R15'}
                                onClick={() => setRandomPairs(15)}
                            />

                            <Tooltip key={'minVolumeX'} title={'Set Minimum Volume For Selection (xMillion)'}>
                                <FormControl>
                                    <CssTextField
                                        id={'candleQty'}
                                        size="small"
                                        // defaultValue={minVolume ? minVolume : '0'}
                                        value={minVolume}
                                        onChange={fnSelectMinVolume}
                                        style={{
                                            width: '70px',
                                            marginLeft: 5, paddingTop: 0, paddingLeft: 5, paddingBottom: 0, margin: 0, paddingTop: 0
                                        }}
                                    /></FormControl>
                            </Tooltip>
                            <Chip key={'219151bctx'}
                                size='small'
                                style={{ border: '1px solid #ccc', marginRight: 5 }}
                                sx={{ borderRadius: 1, backgroundColor: '#67C6E3', color: '#000' }}
                                label={'set'}
                                onClick={fnSetMinVolumeFilter}
                            />
                            <Chip key={'219151asas'}
                                size='small'
                                style={{ border: '1px solid #ccc', marginRight: 5 }}
                                sx={{ borderRadius: 2, backgroundColor: '#67C6E3' }}
                                label={'Hepsi'}
                                onClick={selectA}
                            />

                            <Chip key={'219151casas'}
                                size='small'
                                style={{ border: '1px solid #ccc', marginRight: 5 }}
                                sx={{ borderRadius: 2, backgroundColor: '#67C6E3' }}
                                label={'Hiçbirisi'}
                                onClick={() => {
                                    mode && props.callbackFN({ fname: 'pairs', fvar: [] });
                                    mode && setlastpair(null);
                                    setselectedPairs([])
                                }}
                            />

                            <Chip key={'2asds19151a'}
                                size='small'
                                style={{ border: '1px solid #ccc', marginRight: 5 }}
                                sx={{ borderRadius: 2, backgroundColor: '#67C6E3', minWidth: 45 }}
                                label={'Selected: #' + Array.isArray(selectedPairs) && selectedPairs.length}
                            />

                            <Chip key={'219151bctradingx'}
                                size='small'
                                style={{ border: '1px solid #ccc', marginRight: 5 }}
                                sx={{ borderRadius: 2, backgroundColor: '#FF9800', color: '#000' }}
                                label={'TV'}
                                onClick={openlink}
                            />
                            {/* 
                            <Chip key={'219151bctradingy'}
                                size='small'
                                sx={{ borderRadius: 2, color: '#000', m: '2px' }}
                                // style={{ border: '1px solid #ccc', marginRight: 5 }}
                                // sx={{ borderRadius: 2, backgroundColor: '#FF9800', color: '#000' }}
                                label={'Save Pairs 2 Sandbox'}
                                onClick={savePairs2Sandbox}
                            /> */}

                            {/* <span onClick={savePairs2Sandbox} title={'Save Selected Pairs to Sandbox'} style={{fontSize: 10, color: '#ccc', marginLeft: 5, cursor: 'pointer'}}>{'sv'}</span> */}


                        </>
                    )}
                </Box>

                {isOpened && (
                    <Box sx={{ width: '100%', overflow: 'auto', maxHeight: 300 }}>
                        {/* <div className="flex flex-wrap mt-2 py-2 h-48 overflow-auto border"> */}
                        {Array.isArray(futuresDaily) && futuresDaily.map(tag => {
                            return (
                                <Tooltip title={'Hacim: ' + tag.v} key={tag.s}>
                                    <Chip
                                        style={{ border: '1px solid #ccc', marginRight: 5 }}
                                        checked={selectedPairs.indexOf(tag.s) > -1}
                                        onClick={() => handleChange(tag.s, !(selectedPairs.indexOf(tag.s) > -1))}
                                        label={tag.s}
                                        color={selectedPairs.indexOf(tag.s) > -1 ? 'primary' : undefined}
                                        variant={'filled'}
                                        sx={{ borderRadius: 1, marginTop: 0.5, marginBottom: 0.5, fontSize: 11 }}
                                        size='small'
                                    // variant={selectedTags.indexOf(tag) > -1 ? 'filled' : 'outlined'}
                                    />
                                </Tooltip>

                            )
                        })}
                        {/* </div> */}
                    </Box>
                )}
            </div>

        </>
    );

}
const theme = createTheme({
    components: {
        MuiInputBase: {
            defaultProps: {
                disableInjectingGlobalStyles: true,
            },
        },
    },
});
const CssTextField = styled(TextField)({
    '& label.Mui-focused': {
        color: '#A0AAB4',
    },
    '& .MuiInputBase-input': {
        borderRadius: 8,
        position: 'relative',
        // backgroundColor: theme.palette.mode === 'light' ? '#F3F6F9' : '#1A2027',
        // border: '1px solid',
        // borderColor: theme.palette.mode === 'light' ? '#E0E3E7' : '#2D3843',
        // fontSize: 14,
        // width: 'auto',
        // marginLeft: '5px',
        padding: '4px 6px',
        fontSize: '11px',
        // Use the system font instead of the default Roboto font.
        fontFamily: [
            '-apple-system',
            'BlinkMacSystemFont',
            '"Segoe UI"',
            'Roboto',
            '"Helvetica Neue"',
            'Arial',
            'sans-serif',
            '"Apple Color Emoji"',
            '"Segoe UI Emoji"',
            '"Segoe UI Symbol"',
        ].join(','),
        '&:focus': {
            // boxShadow: `${alpha(theme.palette.primary.main, 0.25)} 0 0 0 0.2rem`,
            // borderColor: theme.palette.primary.main,
        },
    },
});
const fetcher = (url) => fetch(url).then((r) => r.json()).catch(e => console.log('fetcher error', url, e));
function BinanceFuturesDaily(pair) {
    return new Promise(async (resolve) => {
        try {
            var uri = '/api/pub/data/market_futuresdaily_live?paironly=0'
            uri += pair ? '&pair=' + pair : ''
            console.log(uri)
            const data = await fetcher(uri)
            resolve(data)
        } catch (e) {
            console.log('fetch err', e)
        }
    });
}
const numero = (num, dig = 2) => {
    if (num) {
        var resp = parseFloat(num).toLocaleString('tr-TR', {
            minimumFractionDigits: dig,
            maximumFractionDigits: dig
        });
        //console.log('numero', num, resp);
        return resp;
    } else {
        return false;
    }
}
const BattleDefaultParameters = {
    "pairs": [
        "BTCUSDT",
        "ETHUSDT",
        "SOLUSDT",
        "DOGEUSDT",
        "XRPUSDT",
        "BNBUSDT",
        "AVAXUSDT",
        "ADAUSDT",
        "FILUSDT"
    ],
    "candleCounts": 24,
    "intervals": [
        "5m",
        "1m"
    ],
    "battleInterval": [
        "1m"
    ],
}
const ParamTimes = props => {
    const tagsData = ["1m", "5m", "15m", "30m", "1h", "2h", "4h", "6h", "12h", "1d"];
    const [selectedTags, setselectedTags] = useState([]);
    const [candles, setCandles] = useState(100)

    const handleChange = (tag, checked) => {
        if (props.multi) {
            // console.log('selectedTags', selectedTags)
            const nextSelectedTags = checked ? [...selectedTags, tag] : selectedTags.filter(t => t !== tag);
            const newA = [...new Set(nextSelectedTags)];
            setselectedTags(newA);
            props?.callbackFN && props?.callbackFN({ fname: 'intervals', fvar: newA });
        } else {
            const nextSelectedTags = checked ? [tag] : selectedTags.filter(t => t !== tag);
            setselectedTags(nextSelectedTags);
            props?.callbackFN && props?.callbackFN({ fname: 'battleInterval', fvar: nextSelectedTags });

        }
    }
    const handleCandle = (vals) => {
        let nVal = vals?.target?.value ? parseFloat(vals?.target.value) : ''
        setCandles(nVal)
        props.candleCounts && props?.callbackFN && props.callbackFN({ fname: 'candleCounts', fvar: nVal });
    }

    useEffect(() => {
        if (props.battleParams && (props.battleParams.intervals || props.battleParams.battleInterval || props.battleParams.candleCounts)) {
            let parami = props.multi ? props.battleParams.intervals : props.battleParams.battleInterval;
            let bars = props.battleParams && props.battleParams.candleCounts
            bars && candles !== bars && setCandles(bars);
            parami && !_.isEqual(parami, selectedTags) && setselectedTags(parami)
        }
    }, [props.battleParams])

    // useEffect(() => {
    //     console.log('propsz', props)
    //     if (props.battleParams && (props.battleParams.intervals || props.battleParams.battleInterval || props.battleParams.candleCounts)) {
    //         let parami = props.multi ? props.battleParams.intervals : props.battleParams.battleInterval;
    //         let bars = props.battleParams && props.battleParams.candleCounts
    //         // bars && candles !== bars && setCandles(bars);
    //         parami && !_.isEqual(parami, selectedTags) && setselectedTags(parami)
    //     }
    // }, [])

    return (
        <>
            <Box
                sx={{
                    mr: 4,
                    display: 'flex',
                    flexDirection: 'row',
                    alignItems: 'flex-start',
                    justifyContent: 'flex-start',
                }}>

                {tagsData.map(tag => (
                    <Chip
                        key={tag}
                        style={{ border: '1px solid #ccc', marginRight: 5 }}
                        checked={Array.isArray(selectedTags) && selectedTags.indexOf(tag) > -1}
                        onClick={() => handleChange(tag, !(Array.isArray(selectedTags) && selectedTags.indexOf(tag) > -1))}
                        label={tag}
                        color={Array.isArray(selectedTags) && selectedTags.indexOf(tag) > -1 ? 'primary' : undefined}
                        variant={'filled'}
                        sx={{ borderRadius: 2 }}
                    // variant={selectedTags.indexOf(tag) > -1 ? 'filled' : 'outlined'}
                    />
                ))}

                {props.candleCounts && (
                    <Tooltip key={'candleQtyX'} placement="top"
                        sx={{ padding: 0 }}
                        title={'Number of Candles for kline data fetching'}>
                        <FormControl>
                            <CssTextField id="candleQty"
                                size="small"
                                value={candles}
                                onChange={handleCandle}
                                style={{ width: '80px', marginLeft: 5, paddingTop: 0, paddingLeft: 5, paddingBottom: 0, margin: 0, paddingTop: 0, height: 6 }}
                            />
                        </FormControl>

                    </Tooltip>
                )}

            </Box>

        </>
    );
}
const AnomalyList = props => {
    const { user = {}, battleParams } = props;
    const { login } = user;
    const [anomalyList, setAnomalyList] = useState(false)
    const [loading, setLoading] = useState(false)
    const [isOpened, setisOpened] = useState(true);
    const [mode, setMode] = useStickyState(null, 'anomalyList');
    const [anomalyListData, setAnomalyListData] = useState(false);

    const [loadingPairs, setLoadingPairs] = useState(false)
    const [anomalyPairsData, setAnomalyPairsData] = useState(false);

    const [expanded, setExpanded] = React.useState(true);
    const [selectedRows, setSelectedRows] = useState([]);
    const [currentPage, setCurrentPage] = useState(1);
    const itemsPerPage = 25;

    // Filter states
    const [showOnlyAnomalies_f1, setShowOnlyAnomalies_f1] = useState(false);
    const [showOnlyAnomalies, setShowOnlyAnomalies] = useState(false);
    const [includeKlineData, setIncludeKlineData] = useState(false);
    const [includeFundingData, setIncludeFundingData] = useState(false);

    const [includeKlineDatas, setIncludeKlineDatas] = useState({
        '1m': false,
        '5m': false,
        '15m': false,
        '30m': false,
        '1h': false,
        '2h': false,
        '4h': false,
        '6h': false,
        '12h': false,
        '1d': false,
    });

    const [symbolSearch, setSymbolSearch] = useState('');
    // Column visibility states - dynamically generated from data
    const [visibleColumns, setVisibleColumns] = useState({});
    const [columnOptions, setColumnOptions] = useState([]);
    const [dataTimes, setDataTime] = useState('-');

    const handleExpandClick = () => {
        setExpanded(!expanded);
    };

    const [fundingData, setFundingData] = useState(false);
    const [klineData, setKlineData] = useState(false);
    const [klineDatas, setKlineDatas] = useState({});
    //api/pub/ai/get_klines
    const getKlines = async (interval = null) => {
        setLoading(true)
        try {
            let uri = '/api/pub/ai/get_klines' + (interval ? '?interval=' + interval : '');
            const res = await fetch(uri, {
                method: 'POST',
                headers: {
                    'Authorization': 'Bearer ' + props.token,
                    'X-Host': 'Subanet.com',
                },
                body: JSON.stringify(props.battleParams)
            })
            if (!res.ok) {
                var message = `An error has occured: ${res.status} - ${res.statusText}`;
                console.log('Error Code: 981y', message);
                setLoading(false)
                return
            }

            const datax = await res.json();
            // interval && console.log('interval sent', interval);
            // console.log('datax?.data', datax)
            // console.log('battleParams', props.battleParams, datax?.data)
            if (interval && interval !== 'funding') {
                setKlineDatas(prev => {
                    const currKlineDatas = { ...prev };
                    currKlineDatas[interval] = datax?.data;
                    // console.log('currKlineDatas', currKlineDatas);
                    return currKlineDatas;
                });
            } else if (interval && interval == 'funding') {
                setFundingData(datax?.data);
            } else {
                setKlineData(datax?.data);
            }

            setLoading(false)
        }
        catch (e) {
            console.log('e', e)
            alert('Error Code: 981x', e)
            setLoading(false)
        }
    }
    //api/ai/get_anomaly_list
    const getAnomalyList = async () => {
        setLoading(true)
        try {
            let uri = '/api/pub/ai/get_anomaly_list'
            const res = await fetch(uri, {
                method: 'POST',
                headers: {
                    'Authorization': 'Bearer ' + props.token,
                    'X-Host': 'Subanet.com',
                },
            })
            if (!res.ok) {
                var message = `An error has occured: ${res.status} - ${res.statusText}`;
                // alert(message);
                console.log('getAnomalyList error', message);
                setLoading(false)
                return
            }

            const datax = await res.json();
            setAnomalyListData(datax?.data);
            await getKlines();
            props.battleParams.intervals.forEach(async interval => {
                await getKlines(interval);
            });
            await getKlines('funding');
            setLoading(false)
        }
        catch (e) {
            console.log('e', e)
            // alert('Error Code: 981', e)
            setLoading(false)
        }
    }

    //api/ai/get_anomaly_list
    const getAnomalyPairs = async () => {
        setLoadingPairs(true)
        try {
            let uri = '/api/pub/ai/get_anomaly_pairs'
            const res = await fetch(uri, {
                method: 'POST',
                headers: {
                    'Authorization': 'Bearer ' + props.token,
                    'X-Host': 'Subanet.com',
                },
            })
            if (!res.ok) {
                var message = `An error has occured: ${res.status} - ${res.statusText}`;
                // alert(message);
                console.log('getAnomalyPairs error', message);
                setLoadingPairs(false)
                return
            }
            const datax = await res.json();
            console.log('getAnomalyPairs data: ', datax?.data);
            setAnomalyPairsData(datax?.data?.method1);
            setLoadingPairs(false)
        }
        catch (e) {
            console.log('e', e)
            // alert('Error Code: 981', e)
            setLoadingPairs(false)
        }
    }
    // Initialize column options and visibility when data loads
    useEffect(() => {
        if (anomalyListData && anomalyListData.length > 0) {
            // Get all unique keys from the data
            let createdAt;
            try {
                createdAt = new Date(anomalyListData[0]?.created_at).getTime() - (new Date().getTimezoneOffset() * 60000 * 2);
                // console.log('datax?.data', anomalyListData[0]?.created_at
                //     , new Date(anomalyListData[0]?.created_at).getTime()
                //     , new Date().getTimezoneOffset() * 60000
                //     , createdAt
                // );
            } catch (e) {
                console.log('e', e)
            }
            setDataTime(createdAt ? '( ' + (new Date(createdAt)).toUTCString() + ' )' : ' ');

            const allKeys = new Set();
            anomalyListData.forEach(item => {
                Object.keys(item).forEach(key => allKeys.add(key));
            });

            // Convert to array and filter out less important fields
            const keysArray = Array.from(allKeys).filter(key =>
                !key.includes('data') &&
                // !key.includes('created') &&
                // !key.includes('openTime') &&
                !key.includes('closeTime') &&
                !key.includes('firstId') &&
                !key.includes('lastId') &&
                // !key.includes('count') &&
                key !== 'reason_1d' &&
                key !== 'reason_1h' &&
                key !== 'reason_1m'
            );

            setDataTime[anomalyListData[0]?.created_at];
            setColumnOptions(keysArray);

            // Initialize visibility state - show commonly used columns by default
            const initialVisibility = {};
            keysArray.forEach(key => {
                initialVisibility[key] = [
                    "symbol", "priceChange", "priceChangePercent", "weightedAvgPrice", "prevClosePrice",
                    "lastPrice", "openPrice",
                    "highPrice", "lowPrice", "volume", "quoteVolume", "openTime", "closeTime",
                    "count", "openPrice_spread_percent", "range_position_percent",
                    "price_spread_percent"
                ].includes(key);
            });
            setVisibleColumns(initialVisibility);
        }
    }, [anomalyListData]);

    useEffect(() => {
        if (!mode) {
            setLoading(true)
        } else {
            setAnomalyListData(mode?.data);
            setLoading(false)
        }
    }, [mode])

    useEffect(() => {
        if (props.isDataUpdated) {
            getAnomalyList();
        }
    }, [props.isDataUpdated])
    useEffect(() => {
        getAnomalyList();
        getAnomalyPairs();
    }, [])

    // Filter and search logic
    const filteredItems = anomalyListData ? anomalyListData.filter(item => {
        // Filter by "normal" reason if enabled
        if (showOnlyAnomalies && item.reason_combined === 'normal') {
            return false;
        }

        // Filter by symbol search term
        const fn1_pairs = Array.isArray(anomalyPairsData) && anomalyPairsData.map(p => p.Symbol.toLowerCase());
        if (showOnlyAnomalies_f1 && fn1_pairs && item.symbol && !fn1_pairs.includes(item.symbol.toLowerCase())) {
            return false;
        }

        // Filter by symbol search term
        if (symbolSearch && !item.symbol.toLowerCase().includes(symbolSearch.toLowerCase())) {
            return false;
        }

        return true;
    }) : [];

    // Pagination logic
    const indexOfLastItem = currentPage * itemsPerPage;
    const indexOfFirstItem = indexOfLastItem - itemsPerPage;
    const currentItems = filteredItems.slice(indexOfFirstItem, indexOfLastItem);
    const totalPages = Math.ceil(filteredItems.length / itemsPerPage);

    // Handle row selection
    const handleRowSelect = (symbol) => {
        setSelectedRows(prev => {
            if (prev.includes(symbol)) {
                return prev.filter(id => id !== symbol);
            } else {
                return [...prev, symbol];
            }
        });
    };

    // Handle select all
    const handleSelectAll = () => {
        // if (selectedRows.length === currentItems.length) {
        //     // If all items on current page are selected, deselect them
        //     setSelectedRows(prev => {
        //         const currentPageSymbols = currentItems.map(item => item.symbol);
        //         return prev.filter(symbol => !currentPageSymbols.includes(symbol));
        //     });
        // } else {
        //     // Select all items on current page
        //     const currentPageSymbols = currentItems.map(item => item.symbol);
        //     setSelectedRows(prev => {
        //         const newSelected = [...prev];
        //         currentPageSymbols.forEach(symbol => {
        //             if (!newSelected.includes(symbol)) {
        //                 newSelected.push(symbol);
        //             }
        //         });
        //         return newSelected;
        //     });
        // }
        const currentPageSymbols = filteredItems.map(item => item.symbol);
            setSelectedRows(prev => {
                let newSelected = [...prev];
                currentPageSymbols.forEach(symbol => {
                    if (!newSelected.includes(symbol)) {
                        newSelected.push(symbol);
                    } else {
                        newSelected = newSelected.filter(s => s !== symbol);
                    }
                });
                return newSelected;
            });
    };

    // Check if all items on current page are selected
    const isAllSelected = currentItems.length > 0 && currentItems.every(item => selectedRows.includes(item.symbol));

    // Handle column visibility change
    const handleColumnChange = (column) => {
        setVisibleColumns(prev => ({
            ...prev,
            [column]: !prev[column]
        }));
    };

    // Reset pagination when filters change
    useEffect(() => {
        setCurrentPage(1);
    }, [showOnlyAnomalies, showOnlyAnomalies_f1, symbolSearch]);

    return (
        <>
            <Card sx={{ mt: 4, mb: 2, border: 1, mx: 2, borderColor: '#ccc', }}>
                <CardHeader
                    subheader={
                        <Box>
                            Anomaly List {(props.isPreparingData ? '   Yükleniyor...' : '  ')}

                            <span onClick={() => {
                                getAnomalyList();
                                getAnomalyPairs();
                            }} className="text muted text-xs cursor-pointer ml-2">Refresh.   {dataTimes}</span>
                            {/* <span onClick={() => {getAnomalyList(); }} className="text muted text-xs cursor-pointer ml-2">Refresh.</span> */}
                        </Box>
                    }
                    action={
                        <IconButton
                            onClick={handleExpandClick}
                            aria-expanded={expanded}
                            aria-label="show parameters"
                            sx={{
                                transform: expanded ? 'rotate(180deg)' : 'rotate(0deg)',
                                transition: 'transform 0.3s',
                            }}
                        >
                            <ExpandMoreIcon />
                        </IconButton>
                    }
                    // onClick={handleExpandClick}
                    sx={{
                        background: '#efefef', borderRadius: 0,
                        padding: 0, mb: 0, px: 2, py: 1
                    }}
                />
                <Collapse in={expanded} timeout="auto" unmountOnExit>
                    {loading ? (
                        <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
                            <CircularProgress />
                        </Box>
                    ) : (
                        <>
                            {/* Filter and Column Selection Controls */}
                            <Box sx={{
                                display: 'flex',
                                flexWrap: 'wrap',
                                flexDirection: 'column',
                                gap: 2,
                                p: 2,
                                borderBottom: '1px solid #eee',
                                alignItems: 'flex-start'
                            }}>
                                <Stack sx={{ display: 'flex', flexDirection: 'row', gap: 4, alignItems: 'center', justifyContent: 'space-between', mb: 0 }}>


                                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                        <label htmlFor="symbolSearch" style={{ fontSize: '0.875rem' }}>
                                            Symbol:
                                        </label>
                                        <input
                                            type="text"
                                            value={symbolSearch}
                                            onChange={(e) => setSymbolSearch(e.target.value)}
                                            placeholder="Search symbol..."
                                            style={{
                                                padding: '4px 8px',
                                                borderRadius: '4px',
                                                border: '1px solid #ccc',
                                                fontSize: '0.875rem',
                                                width: '120px'
                                            }}
                                        />
                                    </Box>
                                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                        <input
                                            type="checkbox"
                                            checked={showOnlyAnomalies}
                                            onChange={(e) => setShowOnlyAnomalies(e.target.checked)}
                                            id="anomalyFilter"
                                        />
                                        <label htmlFor="anomalyFilter" style={{ fontSize: '12px' }}>
                                            Filter Anomalies
                                        </label>
                                        <input
                                            type="checkbox"
                                            checked={showOnlyAnomalies_f1}
                                            onChange={(e) => setShowOnlyAnomalies_f1(e.target.checked)}
                                            id="anomalyFilter_f1"
                                        />
                                        <label htmlFor="anomalyFilter_f1" style={{ fontSize: '12px' }}>
                                             Fn1 Anom
                                        </label>
                                    </Box>
                                    {/* //add checkbox titled "add kline datas"; */} 
                                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 0 }}>
                                        <Checkbox
                                            checked={includeFundingData}
                                            onChange={(e) => setIncludeFundingData(e.target.checked)}
                                            id="include-kline-data-checkbox"
                                        />
                                        <label
                                            htmlFor="include-kline-data-checkbox"
                                            style={{ cursor: 'pointer', userSelect: 'none', marginLeft: '2px', fontSize: '12px' }}
                                        >
                                            Incl Funding Data
                                        </label>
                                    </Box>
                                    {/* //intervals ta secili olanları da checkbox ile include edelim */}
                                    {props.battleParams && props.battleParams.intervals.map((interval, index) => (
                                        <Box key={index} sx={{ display: 'flex', alignItems: 'center', gap: 0 }}>
                                            <Checkbox
                                                checked={includeKlineDatas[interval]}
                                                onChange={(e) => setIncludeKlineDatas({ ...includeKlineDatas, [interval]: e.target.checked })}
                                                id={`include-kline-data-checkbox-${interval}`}
                                            />
                                            <label
                                                htmlFor={`include-kline-data-checkbox-${interval}`}
                                                style={{ cursor: 'pointer', userSelect: 'none', marginLeft: '2px', fontSize: '12px' }}
                                            >
                                                Incl. {interval} Klines
                                            </label>
                                        </Box>
                                    )
                                )}


                                </Stack>

                                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                                    <Box sx={{ display: 'flex', flexDirection: 'row', justifyContent: 'flex-start', alignItems: 'center' }}>
                                        <label style={{ fontSize: '0.875rem', fontWeight: 'bold' }}>
                                            Select Columns:
                                        </label>

                                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5, ml: 4 }}>
                                            <input
                                                type="checkbox"
                                                checked={Object.values(visibleColumns).every(Boolean)}
                                                onChange={(e) => {
                                                    const newVisibility = {};
                                                    columnOptions.forEach(col => {
                                                        newVisibility[col] = e.target.checked;
                                                    });
                                                    setVisibleColumns(newVisibility);
                                                }}
                                                id="column_select_all"
                                            />
                                            <label
                                                htmlFor="column_select_all"
                                                style={{ fontSize: '0.8125rem', whiteSpace: 'nowrap' }}
                                            >
                                                Select All
                                            </label>
                                        </Box>

                                       
                                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5, ml: 4 }}>
                                            <Chip key={'219151bctxxx'}
                                                size='small'
                                                style={{ border: '1px solid #ccc', marginRight: 5 }}
                                                sx={{ borderRadius: 2, color: '#000' }}
                                                label={'Defaults'}
                                                onClick={() => {
                                                    const defaultColumns = [
                                                        "symbol", "priceChange", "priceChangePercent", "weightedAvgPrice", "prevClosePrice",
                                                        "lastPrice", "openPrice",
                                                        "highPrice", "lowPrice", "volume", "quoteVolume", "openTime", "closeTime",
                                                        "count", "openPrice_spread_percent", "range_position_percent",
                                                        "price_spread_percent"
                                                    ];
                                                    const newVisibility = {};
                                                    columnOptions.forEach(col => {
                                                        newVisibility[col] = defaultColumns.includes(col);
                                                    });
                                                    setVisibleColumns(newVisibility);
                                                }}
                                            />

                                            <Chip key={'MarketO'}
                                                size='small'
                                                style={{ border: '1px solid #ccc', marginRight: 5 }}
                                                sx={{ borderRadius: 2, color: '#000' }}
                                                label={'Market-24hour'}
                                                onClick={() => {
                                                    const defaultColumns = [
                                                        "symbol", "priceChange", "priceChangePercent", "weightedAvgPrice", "prevClosePrice",
                                                        "lastPrice", "lastQty", "bidPrice", "bidQty", "askPrice", "askQty", "openPrice",
                                                        "highPrice", "lowPrice", "volume", "quoteVolume", "openTime", "closeTime",
                                                        "firstId", "lastId", "count"
                                                    ];
                                                    const newVisibility = {};
                                                    columnOptions.forEach(col => {
                                                        newVisibility[col] = defaultColumns.includes(col);
                                                    });
                                                    setVisibleColumns(newVisibility);
                                                }}
                                            />
                                            {/* <input
                                                type="checkbox"
                                                // checked={(() => {
                                                //     const defaultColumns = [
                                                //         'symbol', 'lastPrice', 'priceChangePercent', 
                                                //         'quoteVolume', 'funding_rate_score', 
                                                //         'range_position_percent', 'reason_combined'
                                                //     ];
                                                //     return defaultColumns.every(col => visibleColumns[col]);
                                                // })()}
                                                onChange={(e) => {
                                                    const defaultColumns = [
                                                        'symbol', 'lastPrice', 'priceChangePercent', 
                                                        'quoteVolume', 'funding_rate_score', 
                                                        'range_position_percent', 'reason_combined'
                                                    ];
                                                    const newVisibility = {};
                                                    defaultColumns.forEach(col => {
                                                        if (columnOptions.includes(col)) {
                                                            newVisibility[col] = e.target.checked;
                                                        }
                                                    });
                                                    setVisibleColumns(newVisibility);
                                                }}
                                                id="column_select_default"
                                            />
                                            <label
                                                htmlFor="column_select_default"
                                                style={{ fontSize: '0.8125rem', whiteSpace: 'nowrap' }}
                                            >
                                                Defaults
                                            </label> */}
                                        </Box>

                                    </Box>

                                    <Box sx={{
                                        display: 'grid',
                                        gridTemplateColumns: {
                                            xs: '1fr 1fr',  // 2 columns on extra small screens
                                            sm: '1fr 1fr 1fr',  // 3 columns on small screens
                                            md: '1fr 1fr 1fr 1fr',  // 4 columns on medium screens
                                            lg: '1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr'  // 8 columns on large screens
                                        },
                                        gap: 1,
                                        maxHeight: '150px',
                                        overflowY: 'auto',
                                        border: '1px solid #ccc',
                                        borderRadius: '4px',
                                        padding: '8px'
                                    }}>
                                        {[...columnOptions].sort().map((column, index) => (
                                            <Box key={column} sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                                                <input
                                                    type="checkbox"
                                                    checked={visibleColumns[column] || false}
                                                    onChange={() => handleColumnChange(column)}
                                                    id={`column_${column}`}
                                                />
                                                <label
                                                    htmlFor={`column_${column}`}
                                                    style={{ fontSize: '0.8125rem', whiteSpace: 'nowrap' }}
                                                    title={column}
                                                >
                                                    {column}
                                                </label>
                                            </Box>
                                        ))}
                                    </Box>
                                </Box>

                            </Box>

                            <Box sx={{ overflowX: 'auto', p: 2 }}>
                                <table style={{
                                    width: '100%',
                                    borderCollapse: 'collapse',
                                    fontFamily: 'Arial, sans-serif',
                                    fontSize: '0.875rem'
                                }}>
                                    <thead>
                                        <tr style={{
                                            backgroundColor: '#f5f5f5',
                                            borderBottom: '2px solid #ddd'
                                        }}>
                                            <th style={{
                                                padding: '12px 8px',
                                                textAlign: 'left',
                                                width: '40px'
                                            }}>
                                                <input
                                                    type="checkbox"
                                                    checked={isAllSelected}
                                                    onChange={handleSelectAll}
                                                    style={{ cursor: 'pointer' }}
                                                />
                                            </th>
                                            {columnOptions.map((column) => {
                                                if (visibleColumns[column]) {
                                                    return (
                                                        <th
                                                            key={column}
                                                            style={{
                                                                padding: '12px 8px',
                                                                textAlign: column.includes('Price') || column.includes('Volume') || column.includes('Score') || column.includes('Percent') ? 'right' : 'left',
                                                                fontWeight: '600',
                                                                color: '#333'
                                                            }}
                                                        >
                                                            {column}
                                                        </th>
                                                    );
                                                }
                                                return null;
                                            })}
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {currentItems && currentItems.length > 0 ? (
                                            currentItems.map((item, index) => (
                                                <tr
                                                    key={item.symbol}
                                                    style={{
                                                        borderBottom: '1px solid #eee',
                                                        backgroundColor: index % 2 === 0 ? '#fafafa' : '#fff'
                                                    }}
                                                >
                                                    <td style={{ padding: '10px 8px' }}>
                                                        <input
                                                            type="checkbox"
                                                            checked={selectedRows.includes(item.symbol)}
                                                            onChange={() => handleRowSelect(item.symbol)}
                                                            style={{ cursor: 'pointer' }}
                                                        />
                                                    </td>
                                                    {columnOptions.map((column) => {
                                                        if (visibleColumns[column]) {
                                                            // Special formatting for certain columns
                                                            if (column === 'symbol') {
                                                                return (
                                                                    <td onDoubleClick={() => window.open(`https://www.tradingview.com/chart?symbol=BINANCE%3A${item?.symbol}PERP`, "_blank")} key={`${item.symbol}-${column}`} style={{ padding: '10px 8px', fontWeight: '500' }}>
                                                                        {item[column]}
                                                                    </td>
                                                                );
                                                            } else if (column.includes('Price') || column.includes('price')) {
                                                                return (
                                                                    <td key={`${item.symbol}-${column}`} style={{ padding: '10px 8px', textAlign: 'right' }}>
                                                                        {parseFloat(item[column]).toFixed(4)}
                                                                    </td>
                                                                );
                                                            } else if (column.includes('Percent') || column.includes('percent')) {
                                                                return (
                                                                    <td
                                                                        key={`${item.symbol}-${column}`}
                                                                        style={{
                                                                            padding: '10px 8px',
                                                                            textAlign: 'right',
                                                                            color: parseFloat(item[column]) >= 0 ? '#4caf50' : '#f44336',
                                                                            fontWeight: '500'
                                                                        }}
                                                                    >
                                                                        {parseFloat(item[column]).toFixed(2)}%
                                                                    </td>
                                                                );
                                                            } else if (column.includes('Volume') || column.includes('volume')) {
                                                                return (
                                                                    <td key={`${item.symbol}-${column}`} style={{ padding: '10px 8px', textAlign: 'right' }}>
                                                                        {(parseFloat(item[column]) / 1000000).toFixed(2)}M
                                                                    </td>
                                                                );
                                                            } else if (column.includes('Score') || column.includes('score')) {
                                                                return (
                                                                    <td key={`${item.symbol}-${column}`} style={{ padding: '10px 8px', textAlign: 'right', fontWeight: '500' }}>
                                                                        {item[column]}
                                                                    </td>
                                                                );
                                                            } else if (column.includes('reason') || column.includes('Reason')) {
                                                                return (
                                                                    <td key={`${item.symbol}-${column}`} style={{ padding: '10px 8px', maxWidth: '200px' }}>
                                                                        <div style={{
                                                                            display: 'inline-block',
                                                                            padding: '4px 8px',
                                                                            borderRadius: '4px',
                                                                            backgroundColor: item[column] !== 'normal' ? '#ffebee' : '#f1f8e9',
                                                                            color: item[column] !== 'normal' ? '#c62828' : '#2e7d32',
                                                                            fontSize: '0.75rem',
                                                                            maxWidth: '100%',
                                                                            overflow: 'hidden',
                                                                            textOverflow: 'ellipsis',
                                                                            whiteSpace: 'nowrap'
                                                                        }} title={item[column]}>
                                                                            {item[column]}
                                                                        </div>
                                                                    </td>
                                                                );
                                                            } else {
                                                                // Default formatting for other columns
                                                                return (
                                                                    <td key={`${item.symbol}-${column}`} style={{
                                                                        padding: '10px 8px',
                                                                        textAlign: typeof item[column] === 'number' ? 'right' : 'left'
                                                                    }}>
                                                                        {item[column]}
                                                                    </td>
                                                                );
                                                            }
                                                        }
                                                        return null;
                                                    }).filter(Boolean)}
                                                </tr>
                                            ))
                                        ) : (
                                            <tr>
                                                <td colSpan={columnOptions.filter(col => visibleColumns[col]).length + 1} style={{ padding: '20px', textAlign: 'center', color: '#666' }}>
                                                    No anomaly data available
                                                </td>
                                            </tr>
                                        )}
                                    </tbody>
                                </table>
                            </Box>

                            {/* Pagination Controls */}
                            {totalPages > 1 && (
                                <Box sx={{
                                    display: 'flex',
                                    justifyContent: 'space-between',
                                    alignItems: 'center',
                                    p: 2,
                                    borderTop: '1px solid #eee'
                                }}>
                                    <Box sx={{ color: '#666', fontSize: '0.875rem' }}>
                                        Showing {indexOfFirstItem + 1} to {Math.min(indexOfLastItem, filteredItems.length || 0)} of {filteredItems.length || 0} entries
                                    </Box>
                                        <Box sx={{ display: 'flex', gap: 1 }}>
                                            <Button
                                                size="small"
                                                variant="outlined"
                                                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                                                disabled={currentPage === 1}
                                            >
                                                Previous
                                            </Button>
                                            {[...Array(totalPages)].map((_, i) => {
                                                const pageNum = i + 1;
                                                // Only show first, last, current, and nearby pages
                                                if (pageNum === 1 || pageNum === totalPages || Math.abs(pageNum - currentPage) <= 2) {
                                                    return (
                                                        <Button
                                                            key={`page-${pageNum}`}
                                                            size="small"
                                                            variant={currentPage === pageNum ? "contained" : "outlined"}
                                                            onClick={() => setCurrentPage(pageNum)}
                                                            sx={{
                                                                minWidth: '32px',
                                                                height: '32px',
                                                                padding: '4px'
                                                            }}
                                                        >
                                                            {pageNum}
                                                        </Button>
                                                    );
                                                } else if (Math.abs(pageNum - currentPage) === 3) {
                                                    // Show ellipsis for skipped pages
                                                    return <span key={`ellipsis-${pageNum}`} style={{ padding: '0 8px', lineHeight: '32px' }}>...</span>;
                                                }
                                                return null;
                                            })}
                                            <Button
                                                size="small"
                                                variant="outlined"
                                                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                                                disabled={currentPage === totalPages}
                                            >
                                                Next
                                            </Button>
                                        </Box>
                                </Box>
                            )}
                        </>
                    )}

                    <Box sx={{ p: 2, display: 'flex', justifyContent: 'flex-start' }}>

                        <Button
                            sx={{ ml: 2 }}
                            onClick={() => {
                                // Get selected pairs (symbols)
                                const selectedPairs = selectedRows;

                                // Get selected columns information
                                const selectedColumns = Object.keys(visibleColumns).filter(col => visibleColumns[col]);

                                // Filter the anomalyListData to only include selected pairs
                                const filteredData = anomalyListData.filter(item => selectedPairs.includes(item.symbol));

                                // For each selected pair, include only the selected columns' data
                                const dataToSend = filteredData.filter(item => {
                                    if (showOnlyAnomalies && item.reason_combined === 'normal') {
                                        return false;
                                    }

                                    // Filter by symbol search term
                                    const fn1_pairs = Array.isArray(anomalyPairsData) && anomalyPairsData.map(p => p.Symbol.toLowerCase());
                                    if (showOnlyAnomalies_f1 && fn1_pairs && item.symbol && !fn1_pairs.includes(item.symbol.toLowerCase())) {
                                        return false;
                                    }

                                    // Filter by symbol search term
                                    if (symbolSearch && !item.symbol.toLowerCase().includes(symbolSearch.toLowerCase())) {
                                        return false;
                                    }

                                    return true;
                                }).map(item => {
                                    const filteredItem = {};
                                    selectedColumns.forEach(col => {
                                        filteredItem[col] = item[col];
                                    });
                                    return filteredItem;
                                }); 

                                if (includeFundingData) {
                                    dataToSend.forEach(item => {
                                        try {
                                            item.fundingData = JSON.parse(fundingData.find(i => i.symbol == item.symbol)?.data);
                                        } catch (e) {
                                            console.log('item funding data not found', item.symbol)
                                        }
                                    });
                                }

                                if (includeKlineDatas) {
                                    dataToSend.forEach(item => {
                                        props.battleParams.intervals.forEach(interval => {
                                            // console.log('looking', interval)
                                            if (includeKlineDatas[interval]) {
                                                try {
                                                    item[`klines_${interval}`] = JSON.parse(klineDatas[interval].find(i => i.symbol == item.symbol)?.data);
                                                } catch (e) {
                                                    console.log('item klines not found', item.symbol, interval, klineDatas)
                                                }
                                            }
                                        });
                                    });
                                    // console.log('dataToSend', dataToSend) ;  
                                }

                                   
                                // Call the setAnomalyData function with the filtered data
                                props.setAnomalyData(dataToSend);
                            }}
                        >
                            use this data!
                        </Button>
                    </Box>
                </Collapse>
            </Card>
        </>
    );
}
const PromptCard = props => {

    const [loading, setLoading] = useState(false)
    const [isOpened, setisOpened] = useState(true);
    const [expanded, setExpanded] = React.useState(true);
    const [aiPrompts, setAiPrompts] = useState([]);
    const [selectedPrompt, setSelectedPrompt] = useState(null);
    const [loadingPrompts, setLoadingPrompts] = useState(false);
    const [isEditing, setIsEditing] = useState(false);
    const [editedPrompt, setEditedPrompt] = useState('');
    const [promptTitle, setPromptTitle] = useState('');
    const [aiData, setAiData] = useState('');
    const [aiModels, setAiModels] = useState([]);
    const [selectedModel, setSelectedModel] = useState(null);
    const [localLLMLoading, setLocalLLMLoading] = useState(false);
    const [LLMLoading, setLLMLoading] = useState(false);
    const [fPrompt, setFPrompt] = useState('');

    const [data, setData] = useState(props.anomalyData || []);

    const fetchAIModels = async () => {
        try {
            const response = await fetch('http://localhost:3012/api/pub/ai/localllmlist');
            const data = await response.json();
            if (data.models && Array.isArray(data.models)) {
                setAiModels(data.models);
                // Set the first model as default if none is selected
                if (!selectedModel && data.models.length > 0) {
                    setSelectedModel(data.models[0]);
                }
            }

            setLoadingPrompts(true);
            const response2 = await fetch('http://localhost:3012/api/pub/ai/prompts?type=market');
            const data2 = await response2.json();

            if (data2.prompts) {
                setAiPrompts(data2.prompts);
            }
            setLoadingPrompts(false);

        } catch (e) {
            console.log('Error fetching AI models:', e);
            alert('Error fetching AI models');
            setLoadingPrompts(false);
        }
    };

    useEffect(() => {
        fetchAIModels();
    }, []);

    useEffect(() => {
        const asyncx = async () => {
            // await AIData();
            let pstr = buildPrompt();
            setFPrompt(pstr);
        }
        asyncx();
    }, [props.anomalyData, selectedPrompt]); //aiData, 


    const handleExpandClick = () => {
        setExpanded(!expanded);
    };
    const handleEditPrompt = () => {
        setIsEditing(true);
        setEditedPrompt(selectedPrompt.prompt);
        setPromptTitle(selectedPrompt.title);
    };
    const handleSavePrompt = async () => {
        try {
            // Show popup for title input
            const newTitle = prompt('Enter prompt title:', promptTitle);
            if (newTitle === null) return; // User cancelled

            const promptData = {
                type: 'market',
                promptID: selectedPrompt.promptID,
                title: newTitle,
                prompt: editedPrompt,
                isUpdate: selectedPrompt.title === newTitle // If same title, update existing, otherwise create new
            };

            const response = await fetch('/api/pub/ai/update', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(promptData),
            });

            if (response.ok) {
                // Refresh prompts
                const response = await fetch('http://localhost:3012/api/pub/ai/prompts');
                const data = await response.json();

                if (data.prompts) {
                    setAiPrompts(data.prompts);
                }

                setIsEditing(false);
                alert('Prompt saved successfully!');
            } else {
                alert('Failed to save prompt');
            }
        } catch (e) {
            console.log('Error saving prompt:', e);
            alert('Error saving prompt');
        }
    };
    const handleDeletePrompt = async () => {
        if (!window.confirm('Are you sure you want to delete this prompt?')) {
            return;
        }

        try {
            const response = await fetch('/api/pub/ai/update', {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ promptID: selectedPrompt.promptID }),
            });

            if (response.ok) {
                // Refresh prompts
                const response = await fetch('http://localhost:3012/api/pub/ai/prompts');
                const data = await response.json();

                if (data.prompts) {
                    setAiPrompts(data.prompts);
                }

                setSelectedPrompt(null);
                alert('Prompt deleted successfully!');
            } else {
                alert('Failed to delete prompt');
            }
        } catch (e) {
            console.log('Error deleting prompt:', e);
            alert('Error deleting prompt');
        }
    };
    const handleCancelEdit = () => {
        setIsEditing(false);
        setEditedPrompt(selectedPrompt.prompt);
    };
    const buildPrompt = () => {
        if (selectedPrompt) {
            // let data3 = await fetchMarketStats(true);
            // console.log('props.anomalyData', props.anomalyData)
            let aiDataStg = props.anomalyData; //formatAIData(data, selectedDataSchema);
            let prompt = selectedPrompt.prompt;
            let promptData = prompt
                .replace('<<referans veri >>', JSON.stringify(aiDataStg))
                .replace('<<zaman>>', "" + new Date(new Date(Date.now()) - (new Date().getTimezoneOffset() * 60000)).toISOString());
            return promptData;
        }
    }
    const getLocalLLMResponse = async () => {
        try {
            setLocalLLMLoading(true);
            const promptData = buildPrompt();
            const response = await fetch('/api/pub/ai/localllmquery', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ prompt: promptData, model: selectedModel }),
            });
            const data = await response.json();
            setLocalLLMLoading(false);
            if (data.response) {
                alert(data.response?.aiResponse);
            } else {
                alert('No response');
            }
        } catch (e) {
            console.log('getLocalLLMResponse: Error getting response:', e);
            setLocalLLMLoading(false);
            alert('Error getting response');
        }
    }
    const getGeminiResponse = async (geminiModel = 'gemini-2.5-flash-lite') => {
        let dtBOP = Date.now();
        const promptData = buildPrompt();
        let pBody;
        try {
            pBody = JSON.stringify({ prompt: promptData, model: geminiModel });
        } catch (e) {
            console.log('getGeminiResponse: Error building prompt:', e, promptData, );
        }
//         let respSimule = `         \```json
// {
//   "analysisTimestamp": "2025-08-23T21:56:54.707Z",
//   "analysisLastBarTimestamp": "2025-08-23T21:56:00.000Z",
//   "marketAnalysis": {
//     "overallCondition": "Piyasa, belirgin bir volatiliteye ve işlem hacminde ani artışlara sahip, risk iştahının orta düzeyde olduğu bir dönemde.",
//     "observation": "Özellikle yeni listelenmiş veya son dönemde dikkat çeken altcoin pariteleri, genel piyasa trendlerinden ayrışarak yüksek volatilite ve hacim anomalileri sergilemektedir. Bitcoin ve Ethereum gibi ana akım varlıklarda daha yatay bir seyir gözlemlenirken, bu altcoinlerdeki hareketlilik, yüksek kaldıraç oranları ve ani fiyat değişimleri ile dikkat çekmektedir."
//   },
//   "selectionCriteria": [
//     {
//       "criterion": "Yüksek 24 Saatlik Fiyat Değişimi Yüzdesi",
//       "description": "Scalp trading için uygun olan, kısa sürede yüksek kar potansiyeli taşıyan pariteleri belirlemek amacıyla, son 24 saatte %10'dan fazla fiyat artışı gösteren varlıklar önceliklendirilmiştir."
//     },
//     {
//       "criterion": "Yüksek 24 Saatlik Hacim (QuoteVolume)",
//       "description": "İşlem likiditesini ve giriş/çıkış kolaylığını sağlamak için, son 24 saatte yüksek hacme sahip (en az 100 milyon USDT civarı) pariteler seçilmiştir. Bu, emir defterinin derinliğini ve scalp işlemlerinde slippage'ı minimize etme potansiyelini gösterir."
//     },
//     {
//       "criterion": "Belirgin Fiyat Hareketliliği ve Aralık Kullanımı",
//       "description": "Fiyatın günlük aralığının ortalama üzerinde bir noktada seyretmesi (%range_position_percent > 30), paritenin trendini sürdürme veya momentumunu koruma potansiyeline işaret eder. Bu, scalp işlemleri için daha öngörülebilir bir fiyat hareketi sağlayabilir."
//     },
//     {
//       "criterion": "Yüksek Fiyat Volatilitesi (price_spread_percent)",
//       "description": "Scalp trading stratejileri için gerekli olan kısa süreli alım-satım fırsatlarını yaratacak yüksek fiyat oynaklığına sahip pariteler tercih edilmiştir. Belirlenen eşik değerin üzerindeki (örn. %50) fiyat farkı, alım-satım aralıklarının geniş olduğunu gösterir."
//     },
//     {
//       "criterion": "Anomali Tespit Edilmiş Yüksek Risk İştahı",
//       "description": "Risk iştahının yüksek olduğu bir piyasa ortamında, ortalamanın üzerinde volatilite, hacim veya fiyat değişimi gösteren ve bu durumun istatistiksel olarak anlamlı olduğu (anomaly detection sistemimiz tarafından işaretlenen) pariteler, yüksek ödül potansiyeli nedeniyle değerlendirilmiştir."
//     }
//   ],
//   "recommendedPairs": [
//     {
//       "symbol": "WLFIUSDT",
//       "confidenceScore": 92,
//       "analysis": {
//         "marketAnomaly": "Piyasa geneline kıyasla olağanüstü yüksek fiyat değişim yüzdesi (%63.83) ve geniş fiyat aralığı (%price_spread_percent: 106.19). Bu, güçlü bir momentum ve işlem iştahına işaret ediyor.",
//         "internalAnomaly": "Yüksek gün içi volatilite ve fiyatın %36.69'luk diliminde olması, mevcut trendin devam edebileceği sinyali veriyor. Yüksek quoteVolume ve trade count, likiditenin yeterli olduğunu gösteriyor."
//       }
//     },
//     {
//       "symbol": "PROMPTUSDT",
//       "confidenceScore": 88,
//       "analysis": {
//         "marketAnomaly": "Son derece yüksek fiyat değişim yüzdesi (%115.91) ve fiyatın 24 saatlik aralığın %89.24'ünde olması, güçlü bir yükseliş momentumuna ve kısa vadeli fırsatlara işaret ediyor.",
//         "internalAnomaly": "Son 4 saatlik kline verileri, fiyatın önemli bir yükseliş eğiliminde olduğunu ve yüksek hacimle desteklendiğini gösteriyor. Yüksek volatilite (%price_spread_percent: 60.16) scalp trading için elverişli."
//       }
//     },
//     {
//       "symbol": "FHEUSDT",
//       "confidenceScore": 85,
//       "analysis": {
//         "marketAnomaly": "Son 24 saatteki %14.06'luk fiyat artışı ve yüksek hacim (%quoteVolume: 1.2B USDT), dikkat çekici. Günlük aralıkta fiyatın %17.23 seviyesinde olması, bir miktar konsolidasyon sonrası yeni bir hareket potansiyeline işaret edebilir.",
//         "internalAnomaly": "4 saatlik ve 1 saatlik kline verileri, fiyatın belirli bir bantta hareket ettiğini ancak yüksek hacimle desteklenen kısa süreli ani yükselişlerin olduğunu gösteriyor. Bu tür küçük fiyat sıçramaları, scalp fırsatları yaratabilir."
//       }
//     },
//     {
//       "symbol": "MEMEUSDT",
//       "confidenceScore": 82,
//       "analysis": {
//         "marketAnomaly": "Piyasa genelindeki ortalamanın oldukça üzerinde bir hacim (%quoteVolume: 1.07B USDT) ve fiyat değişim yüzdesi (%39.14), bu pariteye olan ilgiyi ve işlem iştahını gösteriyor.",
//         "internalAnomaly": "Fiyatın günlük aralığın %58.55'inde olması ve yüksek volatilite (%price_spread_percent: 48.38), kısa vadeli alım-satım için cazip koşullar sunuyor. Özellikle son 1m kline'lardaki hızlı fiyat hareketleri dikkat çekici."
//       }
//     },
//     {
//       "symbol": "BNBUSDT",
//       "confidenceScore": 78,
//       "analysis": {
//         "marketAnomaly": "Son 24 saatte %45.83'lük önemli bir fiyat artışı ve yüksek hacim (%quoteVolume: 111M USDT) gösteriyor. Fiyatın günlük aralığın %67.12'sinde olması, trendin devam edebileceği sinyali veriyor.",
//         "internalAnomaly": "Yüksek volatilite (%price_spread_percent: 47%) ve son 4 saatlik kline'larda gözlenen artan hacim, scalp işlemleri için uygun bir zemin hazırlıyor. Özellikle son 1m kline'lardaki fiyat hareketleri, kısa vadeli işlem fırsatları sunabilir."
//       }
//     }
//   ],
//   "strategies": [
//     {
//       "symbol": "WLFIUSDT",
//       "direction": "Long",
//       "logic": "WLFIUSDT, son 24 saatteki güçlü fiyat artışı (%63.83) ve yüksek hacmiyle öne çıkıyor. Fiyatın mevcut aralıkta orta seviyelerde olması ve son 1m kline'lardaki momentumun devam etme potansiyeli, kısa vadeli bir yukarı yönlü hareketi destekliyor. Risk/ödül oranı cazip seviyede.",
//       "entryLevels": [
//         {
//           "type": "Primary",
//           "level": "0.3280000",
//           "logic": "Mevcut fiyat seviyesinin hafif üzerinde, kısa vadeli direnç kırılımı beklentisiyle."
//         },
//         {
//           "type": "Secondary",
//           "level": "0.3250000",
//           "logic": "Daha düşük bir giriş seviyesi için, olası geri çekilmelerde ortalama maliyeti düşürmek amacıyla."
//         }
//       ],
//       "timeframe": {
//         "tradeDuration": "15",
//         "recommendationValidity": "30"
//       },
//       "riskManagement": {
//         "stopLoss": {
//           "level": "0.3200000",
//           "logic": "Giriş seviyesinin altında, olası bir trend dönüşü veya piyasa düzeltmesi durumunda zararı sınırlamak için."
//         },
//         "riskCalculationLogic": "Pozisyon büyüklüğü, stop-loss seviyesi ile belirlenen maksimum risk toleransına (örneğin, toplam sermayenin %1'i) göre ayarlanmalıdır. Bu, her işlem için riski kontrol altında tutar."
//       },
//       "takeProfitLevels": [
//         {
//           "level": "0.3370000",
//           "riskRewardRatio": "1:1.5",
//           "logic": "Giriş seviyesinden %3'lük bir artış hedefi. Bu seviye, yakın vadeli bir direnç veya psikolojik eşik olabilir. Risk/ödül oranı 1:1.5 olarak hedeflenmiştir (0.15 / 0.10)."
//         },
//         {
//           "level": "0.3450000",
//           "riskRewardRatio": "1:2.5",
//           "logic": "Daha agresif bir hedef seviyesi, güçlü momentum durumunda kârı maksimize etmek için. Bu, giriş seviyesinden yaklaşık %5'lik bir artışı temsil eder."
//         }
//       ]
//     },
//     {
//       "symbol": "PROMPTUSDT",
//       "direction": "Long",
//       "logic": "PROMPTUSDT'nin son 24 saatteki %115.91'lik artışı ve %89'luk aralık kullanımı, son derece güçlü bir yukarı momentumu işaret ediyor. Yüksek volatilite ve hacim, scalp trading için ideal bir ortam sunar. Kısa vadeli düzeltmelerden sonra trendin devam etmesi beklenmektedir.",
//       "entryLevels": [
//         {
//           "type": "Primary",
//           "level": "0.2780000",
//           "logic": "Mevcut fiyatın hemen altında, kırılma sonrası giriş için uygun seviye."
//         },
//         {
//           "type": "Secondary",
//           "level": "0.2750000",
//           "logic": "Olası bir geri çekilmede daha agresif bir giriş noktası."
//         }
//       ],
//       "timeframe": {
//         "tradeDuration": "10",
//         "recommendationValidity": "20"
//       },
//       "riskManagement": {
//         "stopLoss": {
//           "level": "0.2700000",
//           "logic": "Giriş seviyesinin altında, güçlü bir destek seviyesi veya önceki direnç seviyesinin altına inilmemesi prensibiyle belirlenmiştir."
//         },
//         "riskCalculationLogic": "Pozisyon büyüklüğü, sermayenin %1'inden fazla risk oluşturmayacak şekilde ayarlanmalı ve stop-loss seviyesi dikkate alınarak hesaplanmalıdır."
//       },
//       "takeProfitLevels": [
//         {
//           "level": "0.2830000",
//           "riskRewardRatio": "1:1.7",
//           "logic": "İlk hedef, giriş seviyesinden yaklaşık %1.8'lik bir artış. R:R oranı yaklaşık 1:1.7 olacaktır."
//         },
//         {
//           "level": "0.2880000",
//           "riskRewardRatio": "1:3.6",
//           "logic": "Daha iddialı bir hedef. Bu seviye, momentumun devamı halinde ulaşılabilir ve R:R oranı yaklaşık 1:3.6'dır."
//         }
//       ]
//     },
//     {
//       "symbol": "FHEUSDT",
//       "direction": "Long",
//       "logic": "FHEUSDT, önemli bir 24 saatlik fiyat artışı (%14.06) ve yüksek işlem hacmiyle dikkat çekiyor. Son 4 saatlik ve 1 saatlik kline'lar, fiyatın yeniden yukarı yönlü hareket potansiyeli olduğunu ve işlem hacminin bu hareketi desteklediğini gösteriyor. Scalp için uygun kısa vadeli momentum yakalanabilir.",
//       "entryLevels": [
//         {
//           "type": "Primary",
//           "level": "0.0850000",
//           "logic": "Mevcut fiyat seviyesinin hemen altında, destek olarak çalışabilecek bir bölge."
//         },
//         {
//           "type": "Secondary",
//           "level": "0.0830000",
//           "logic": "Geri çekilme durumunda daha düşük bir giriş noktası, riskin dağıtılması için."
//         }
//       ],
//       "timeframe": {
//         "tradeDuration": "15",
//         "recommendationValidity": "30"
//       },
//       "riskManagement": {
//         "stopLoss": {
//           "level": "0.0815000",
//           "logic": "Önemli bir destek seviyesinin altında, piyasa aleyhine gelişebilecek bir durumda pozisyonu kapatmak için."
//         },
//         "riskCalculationLogic": "Stop-loss ile riskin belirlenmesi ve pozisyon büyüklüğünün buna göre ayarlanması, sermayenin %1'lik risk limiti dahilinde tutulmasını sağlar."
//       },
//       "takeProfitLevels": [
//         {
//           "level": "0.0865000",
//           "riskRewardRatio": "1:1.6",
//           "logic": "İlk hedef, mevcut fiyat seviyesinden yaklaşık %1.8'lik bir artış ile belirlenmiştir. R:R oranı yaklaşık 1:1.6'dır."
//         },
//         {
//           "level": "0.0880000",
//           "riskRewardRatio": "1:3.3",
//           "logic": "Daha yüksek bir hedef, momentumun devam etmesi durumunda kârı maksimize etmek için. Bu seviyede R:R oranı yaklaşık 1:3.3'tür."
//         }
//       ]
//     },
//     {
//       "symbol": "MEMEUSDT",
//       "direction": "Long",
//       "logic": "MEMEUSDT, yüksek hacim (%1.07B) ve volatilite (%price_spread_percent: 48.38) ile dikkat çekiyor. Fiyatın günlük aralığın ortasında yer alması (%range_position_percent: 58.55), mevcut yükselişin devam etme potansiyelini gösteriyor. Short-term scalp fırsatları için uygun.",
//       "entryLevels": [
//         {
//           "type": "Primary",
//           "level": "0.0036000",
//           "logic": "Mevcut fiyat seviyesinin hafif altında, hızlı bir tepki alımı için."
//         },
//         {
//           "type": "Secondary",
//           "level": "0.0035800",
//           "logic": "Geri çekilme durumunda pozisyon eklemek için."
//         }
//       ],
//       "timeframe": {
//         "tradeDuration": "10",
//         "recommendationValidity": "20"
//       },
//       "riskManagement": {
//         "stopLoss": {
//           "level": "0.0035500",
//           "logic": "Fiyatın önceki düşük seviyelerin altına sarkmasını engellemek ve risk yönetimi sağlamak için."
//         },
//         "riskCalculationLogic": "Pozisyon büyüklüğü, stop-loss ile belirlenen risk oranına göre ayarlanmalı, toplam sermayenin riskinin %1'i aşılmamalıdır."
//       },
//       "takeProfitLevels": [
//         {
//           "level": "0.0036500",
//           "riskRewardRatio": "1:1.7",
//           "logic": "İlk hedef, giriş seviyesinden %1.4'lük bir artış ile belirlenmiştir. R:R oranı yaklaşık 1:1.7'dir."
//         },
//         {
//           "level": "0.0037200",
//           "riskRewardRatio": "1:3.7",
//           "logic": "Daha iddialı bir hedef, momentumun devamı halinde kârı maksimize etmek için. R:R oranı yaklaşık 1:3.7'dir."
//         }
//       ]
//     },
//     {
//       "symbol": "BNBUSDT",
//       "direction": "Long",
//       "logic": "BNBUSDT, son 24 saatte %45.83'lük önemli bir fiyat artışı ve yüksek hacimle öne çıkıyor. Gün içi fiyat hareketi ve yüksek volatilite, scalp trading için fırsatlar sunuyor. Düşük riskle pozisyon almak için mevcut fiyat seviyeleri uygun görünüyor.",
//       "entryLevels": [
//         {
//           "type": "Primary",
//           "level": "50.00000",
//           "logic": "Mevcut fiyatın hafif altında, olası bir destek seviyesinden alım fırsatı."
//         },
//         {
//           "type": "Secondary",
//           "level": "48.00000",
//           "logic": "Daha belirgin bir geri çekilme durumunda pozisyonu artırmak için."
//         }
//       ],
//       "timeframe": {
//         "tradeDuration": "15",
//         "recommendationValidity": "30"
//       },
//       "riskManagement": {
//         "stopLoss": {
//           "level": "47.00000",
//           "logic": "Pozisyon büyüklüğünü riske göre ayarlayarak, stop-loss seviyesi ile potansiyel zararı minimize etmek."
//         },
//         "riskCalculationLogic": "Pozisyon büyüklüğü, sermayenin %1'lik risk toleransı ve stop-loss seviyesi dikkate alınarak hesaplanmalıdır."
//       },
//       "takeProfitLevels": [
//         {
//           "level": "52.00000",
//           "riskRewardRatio": "1:1.6",
//           "logic": "İlk hedef, giriş seviyesinden yaklaşık %4'lük bir artış. R:R oranı yaklaşık 1:1.6'dır."
//         },
//         {
//           "level": "54.00000",
//           "riskRewardRatio": "1:3.3",
//           "logic": "Daha iddialı bir hedef, momentumun devamı durumunda kârı maksimize etmek için. R:R oranı yaklaşık 1:3.3'tür."
//         }
//       ]
//     },
//     {
//       "symbol": "SOLUSDT",
//       "direction": "Long",
//       "logic": "SOLUSDT, son 24 saatte güçlü bir fiyat artışı (%3.7) ve yüksek hacim (%674M) sergiliyor. Fiyatın günlük aralığın ortasında yer alması ve son 4 saatlik kline'larda gözlenen yukarı yönlü momentum, kısa vadeli bir yükselişi destekliyor. Scalp işlemleri için uygun.",
//       "entryLevels": [
//         {
//           "type": "Primary",
//           "level": "203.0000",
//           "logic": "Mevcut fiyat seviyesinin hafif altında, güçlü bir destek noktası."
//         },
//         {
//           "type": "Secondary",
//           "level": "200.0000",
//           "logic": "Olası bir geri çekilmede ekleme yapmak için."
//         }
//       ],
//       "timeframe": {
//         "tradeDuration": "15",
//         "recommendationValidity": "30"
//       },
//       "riskManagement": {
//         "stopLoss": {
//           "level": "195.0000",
//           "logic": "Önceki düşük seviyelerin altına inildiğinde işlemi kapatmak, zararı sınırlamak için."
//         },
//         "riskCalculationLogic": "Pozisyon büyüklüğü, stop-loss ile belirlenen risk toleransı dahilinde (sermayenin %1'i) hesaplanmalıdır."
//       },
//       "takeProfitLevels": [
//         {
//           "level": "207.0000",
//           "riskRewardRatio": "1:1.5",
//           "logic": "İlk hedef, giriş seviyesinden yaklaşık %2'lik bir artış. R:R oranı yaklaşık 1:1.5'tir."
//         },
//         {
//           "level": "212.0000",
//           "riskRewardRatio": "1:2.6",
//           "logic": "Daha iddialı bir hedef, momentumun devamı halinde kârı maksimize etmek için. R:R oranı yaklaşık 1:2.6'dır."
//         }
//       ]
//     }
//   ]
// }
// \```
//         `;
        props.setAiResponse(respSimule);
        setExpanded(false);
        return;
        try {
            setLLMLoading(true);
            const response = await fetch('/api/pub/ai/llmquery', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: pBody,
            });
            const data = await response.json();
            console.log('getGeminiResponse: Response time:', Date.now() - dtBOP, 'ms');
            setLLMLoading(false);
            if (data.response) {
                // console.log(data.response?.aiResponse);
                props.setAiResponse(data.response?.aiResponse);
                // Collapse the Prompt Setup card after receiving response
                setExpanded(false);
            } else {
                alert('No response');
            }
        } catch (e) {
            console.log('getGeminiResponse: Error getting response:', e, promptData);
            setLLMLoading(false);
            alert('Error getting response');
        }
    }
    const CopyPrompt2Clipboard = () => {
        navigator.clipboard.writeText(fPrompt);
        alert('Prompt copied to clipboard');
    }
    const copyData = () => {
        navigator.clipboard.writeText(JSON.stringify((props.anomalyData)));
        alert('Prompt datavalues copied to clipboard');
    }
    // const refreshData = async () => {
    //     try {
    //         await fetchAIModels(true);
    //         // console.log('data', data)
    //     } catch (e) { }
    // }
    return (
        <>
            <Card sx={{ mt: 2, mb: 2, border: 1, mx: 2, borderColor: '#ccc', }}>
                <CardHeader
                    subheader={
                        <Box>
                            Prompt Setup {(props.isPreparingData ? '   Yükleniyor...' : '')}

                        </Box>
                    }
                    action={
                        <IconButton
                            onClick={handleExpandClick}
                            aria-expanded={expanded}
                            aria-label="show parameters"
                            sx={{
                                transform: expanded ? 'rotate(180deg)' : 'rotate(0deg)',
                                transition: 'transform 0.3s',
                            }}
                        >
                            <ExpandMoreIcon />
                        </IconButton>
                    }
                    // onClick={handleExpandClick}
                    sx={{
                        background: '#efefef', borderRadius: 0,
                        padding: 0, mb: 0, px: 2, py: 1
                    }}
                />
                <Collapse in={expanded} timeout="auto" unmountOnExit>
                    {loading ? (
                        <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
                            <CircularProgress />
                        </Box>
                    ) : (
                        <Box sx={{ p: 2, }}>
                            <>

                                {loadingPrompts ? (
                                    <Box sx={{
                                        display: 'flex',
                                        flexDirection: 'column',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        minHeight: '200px',
                                        gap: 2
                                    }}>
                                        <Box sx={{
                                            position: 'relative',
                                            display: 'inline-flex'
                                        }}>
                                            <CircularProgress size={60} thickness={4} />
                                            <Box
                                                sx={{
                                                    top: 0,
                                                    left: 0,
                                                    bottom: 0,
                                                    right: 0,
                                                    position: 'absolute',
                                                    display: 'flex',
                                                    alignItems: 'center',
                                                    justifyContent: 'center',
                                                }}
                                            >
                                                <Typography
                                                    variant="caption"
                                                    component="div"
                                                    color="text.secondary"
                                                >
                                                    AI
                                                </Typography>
                                            </Box>
                                        </Box>
                                        <Typography variant="h6" color="text.secondary">
                                            Loading AI Prompts
                                        </Typography>
                                        <Typography variant="body2" color="text.secondary">
                                            Please wait while we fetch the latest data...
                                        </Typography>
                                        <LinearProgress
                                            sx={{
                                                width: '100%',
                                                borderRadius: 10
                                            }}
                                        />
                                    </Box>
                                ) : (
                                    <>
                                        <Autocomplete
                                            options={aiPrompts}
                                            getOptionLabel={(option) => option.title}
                                            onChange={(event, newValue) => {
                                                setSelectedPrompt(newValue);
                                            }}
                                            renderInput={(params) => (
                                                <TextField
                                                    {...params}
                                                    label="Select a prompt"
                                                    variant="outlined"
                                                    fullWidth
                                                />
                                            )}
                                            sx={{ width: '100%', marginBottom: '20px' }}
                                        />
                                        {selectedPrompt && (
                                            <>

                                                <Box sx={{ display: 'flex', flexDirection: 'row', justifyContent: 'space-between' }}>

                                                    <Box sx={{ display: 'flex', flexDirection: 'row', justifyContent: 'center', alignItems: 'center' }}>

                                                        <FormControl sx={{ width: 200 }}>
                                                            <Select
                                                                value={selectedModel?.model || ''}
                                                                onChange={(event) => {
                                                                    const newValue = aiModels.find(model => model.model === event.target.value);
                                                                    setSelectedModel(newValue || null);
                                                                }}
                                                                sx={{ p: 0, px: 2 }}
                                                                displayEmpty
                                                                renderValue={(selected) => {
                                                                    if (!selected) {
                                                                        return <em>Select a model</em>;
                                                                    }
                                                                    const model = aiModels.find(m => m.model === selected);
                                                                    return model ? model.name : '';
                                                                }}
                                                            >
                                                                <MenuItem value="">
                                                                    <em>Select a model</em>
                                                                </MenuItem>
                                                                {aiModels.map((model) => (
                                                                    <MenuItem key={model.model} value={model.model}>
                                                                        {model.name}
                                                                    </MenuItem>
                                                                ))}
                                                            </Select>
                                                        </FormControl>
                                                        <Box sx={{ display: 'flex', flexDirection: 'row', justifyContent: 'flex-end', ml: 1, gap: 1 }}>


                                                            <Chip key={'localLLMLoading'}
                                                                size='small'
                                                                style={{ border: '1px solid #ccc', marginRight: 5 }}
                                                                sx={{ borderRadius: 2, color: '#000' }}
                                                                label={!localLLMLoading ? 'Local LLM' : ' loading... '}
                                                                onClick={getLocalLLMResponse}
                                                                disabled={localLLMLoading}
                                                                loading={localLLMLoading}
                                                            />

                                                            <Chip key={'LLMLoading'}
                                                                size='small'
                                                                style={{ border: '1px solid #ccc', marginRight: 5 }}
                                                                sx={{ borderRadius: 2, backgroundColor: '#FF9800', color: '#000' }}
                                                                label={!LLMLoading ? 'Cloud LLM' : ' loading... '}
                                                                onClick={() => getGeminiResponse('gemini-2.5-flash-lite')}
                                                                disabled={LLMLoading}
                                                                loading={LLMLoading}
                                                            />

                                                            <Chip key={'LLMLoading2'}
                                                                size='small'
                                                                style={{ border: '1px solid #ccc', marginRight: 5 }}
                                                                sx={{ borderRadius: 2, backgroundColor: '#FF9800', color: '#000' }}
                                                                label={!LLMLoading ? 'Cloud LLM+' : ' loading... '}
                                                                onClick={() => getGeminiResponse('gemini-2.0-flash')}
                                                                disabled={LLMLoading}
                                                                loading={LLMLoading}
                                                            />
                                                            {/*                                                                     
                                                        <Button
                                                            variant="outlined"
                                                            size="small"
                                                            onClick={getGeminiResponse}
                                                            disabled={LLMLoading}
                                                            sx={{ mr: 1, minWidth: '120px' }}
                                                        >
                                                            {LLMLoading ? (
                                                                <CircularProgress size={20} />
                                                            ) : (
                                                                'Cloud AI'
                                                            )}

                                                        </Button> */}

                                                            <Chip key={'CopyPrompt2Clipboard'}
                                                                size='small'
                                                                style={{ border: '1px solid #ccc', marginRight: 5 }}
                                                                sx={{ borderRadius: 2, color: '#000' }}
                                                                label={'Copy All Clipboard'}
                                                                onClick={CopyPrompt2Clipboard}
                                                            />

                                                            <Chip key={'copyData'}
                                                                size='small'
                                                                style={{ border: '1px solid #ccc', marginRight: 5 }}
                                                                sx={{ borderRadius: 2, color: '#000' }}
                                                                label={'Copy Data Clipboard'}
                                                                onClick={copyData}
                                                            />


                                                            <Chip key={'qwen'}
                                                                size='small'
                                                                style={{ border: '1px solid #ccc', marginRight: 5 }}
                                                                sx={{ ml: 2, backgroundColor: '#FF9800', borderRadius: 2, color: '#000' }}
                                                                label={'open Qwen'}
                                                                onClick={() => window.open('https://chat.qwen.ai/', "_blank")}
                                                            />

                                                            <Chip key={'aistudio'}
                                                                size='small'
                                                                style={{ border: '1px solid #ccc', marginRight: 5 }}
                                                                sx={{ backgroundColor: '#FF9800', borderRadius: 2, color: '#000' }}
                                                                label={'Gemini'}
                                                                onClick={() => window.open('https://aistudio.google.com/prompts/new_chat', "_blank")}
                                                            />

                                                            <Chip key={'chatgpt'}
                                                                size='small'
                                                                style={{ border: '1px solid #ccc', marginRight: 5 }}
                                                                sx={{ backgroundColor: '#FF9800', borderRadius: 2, color: '#000' }}
                                                                label={'chatgpt'}
                                                                onClick={() => window.open('https://chatgpt.com/', "_blank")}
                                                            />
                                                        </Box>
                                                    </Box>
                                                    <Box sx={{ display: 'flex', flexDirection: 'row', justifyContent: 'flex-end' }}>
                                                        {isEditing ? (
                                                            <>
                                                                <Button
                                                                    variant="outlined"
                                                                    size="small"
                                                                    onClick={handleSavePrompt}
                                                                    sx={{ mr: 1 }}
                                                                >
                                                                    Save
                                                                </Button>
                                                                <Button
                                                                    variant="outlined"
                                                                    size="small"
                                                                    onClick={handleCancelEdit}
                                                                >
                                                                    Cancel
                                                                </Button>
                                                            </>
                                                        ) : (
                                                            <>
                                                                <Button
                                                                    variant="outlined"
                                                                    size="small"
                                                                    onClick={handleEditPrompt}
                                                                    sx={{ mr: 1 }}
                                                                >
                                                                    Edit
                                                                </Button>
                                                                <Button
                                                                    variant="outlined"
                                                                    size="small"
                                                                    color="error"
                                                                    onClick={handleDeletePrompt}
                                                                >
                                                                    Delete
                                                                </Button>
                                                            </>
                                                        )}
                                                    </Box>
                                                </Box>
                                                <Box sx={{ mt: 2, p: 2, border: '1px solid #ccc', borderRadius: '4px', backgroundColor: '#f9f9f9', position: 'relative', maxHeight: 500, overflow: 'auto' }}>
                                                    {isEditing ? (
                                                        <TextField
                                                            fullWidth
                                                            multiline
                                                            rows={10}
                                                            value={editedPrompt}
                                                            onChange={(e) => setEditedPrompt(e.target.value)}
                                                            variant="outlined"
                                                        />
                                                    ) : (
                                                        <Typography variant="body1" sx={{ whiteSpace: 'pre-wrap' }}>
                                                            {fPrompt}
                                                        </Typography>
                                                    )}
                                                </Box>
                                            </>
                                        )}
                                    </>
                                )}
                            </>
                        </Box>
                    )}
                </Collapse>
            </Card>
        </>
    )
}


const CloudLLMResponse = (props) => {
    const [expanded, setExpanded] = useState(true);

    const handleExpandClick = () => {
        setExpanded(!expanded);
    };

    const openTradingView = (symbol) => {
        const urlX = `https://www.tradingview.com/chart?symbol=BINANCE%3A${symbol}PERP`;
        window.open(urlX, "_blank");
    };

    // Function to format the AI response in a modern, readable way
    const formatAIResponse = (response) => {
        if (!response) return <Typography>No response available</Typography>;

        // If it's already a string, try to parse it as JSON
        let data;
        try {
            data = typeof response === 'string' ? JSON.parse(response) : response;
        } catch (e) {
            // If parsing fails, treat as plain text
            return <Typography sx={{ whiteSpace: 'pre-wrap' }}>{response}</Typography>;
        }

        // If it's a JSON object with the expected structure, render the modern table
        if (typeof data === 'object' && data.recommendedPairs && data.strategies) {
            return (
                <Box>
                    {/* Market Analysis Summary */}
                    {data.marketAnalysis && (
                        <Card sx={{ mb: 3, border: 1, borderColor: '#e0e0e0' }}>
                            <CardHeader
                                title="Market Analysis"
                                sx={{ background: '#f8f9fa', py: 1 }}
                            />
                            <Box sx={{ p: 2 }}>
                                <Typography variant="body2" sx={{ mb: 1, fontWeight: 'bold' }}>
                                    Overall Condition:
                                </Typography>
                                <Typography variant="body2" sx={{ mb: 2 }}>
                                    {data.marketAnalysis.overallCondition}
                                </Typography>
                                <Typography variant="body2" sx={{ mb: 1, fontWeight: 'bold' }}>
                                    Observation:
                                </Typography>
                                <Typography variant="body2">
                                    {data.marketAnalysis.observation}
                                </Typography>
                            </Box>
                        </Card>
                    )}

                    {/* Recommended Pairs Table */}
                    <Card sx={{ mb: 3, border: 1, borderColor: '#e0e0e0' }}>
                        <CardHeader
                            title="Recommended Trading Pairs"
                            sx={{ background: '#f8f9fa', py: 1 }}
                        />
                        <Box sx={{ p: 2 }}>
                            <Box sx={{ overflowX: 'auto' }}>
                                <table style={{ width: '100%', borderCollapse: 'collapse' }}>
                                    <thead>
                                        <tr style={{ backgroundColor: '#f5f5f5' }}>
                                            <th style={{ padding: '12px 8px', textAlign: 'left', borderBottom: '2px solid #ddd', fontWeight: 'bold' }}>Symbol</th>
                                            <th style={{ padding: '12px 8px', textAlign: 'center', borderBottom: '2px solid #ddd', fontWeight: 'bold' }}>Confidence</th>
                                            <th style={{ padding: '12px 8px', textAlign: 'left', borderBottom: '2px solid #ddd', fontWeight: 'bold' }}>Market Anomaly</th>
                                            <th style={{ padding: '12px 8px', textAlign: 'left', borderBottom: '2px solid #ddd', fontWeight: 'bold' }}>Internal Anomaly</th>
                                            <th style={{ padding: '12px 8px', textAlign: 'center', borderBottom: '2px solid #ddd', fontWeight: 'bold' }}>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {data.recommendedPairs.map((pair, index) => (
                                            <tr key={index} style={{ borderBottom: '1px solid #eee' }}>
                                                <td style={{ padding: '12px 8px' }}>
                                                    <Typography
                                                        variant="body2"
                                                        sx={{
                                                            fontWeight: 'bold',
                                                            color: '#1976d2',
                                                            cursor: 'pointer',
                                                            '&:hover': { textDecoration: 'underline' }
                                                        }}
                                                        onDoubleClick={() => openTradingView(pair.symbol)}
                                                    >
                                                        {pair.symbol}
                                                    </Typography>
                                                </td>
                                                <td style={{ padding: '12px 8px', textAlign: 'center' }}>
                                                    <Chip
                                                        label={`${pair.confidenceScore}%`}
                                                        color={pair.confidenceScore >= 90 ? 'success' : pair.confidenceScore >= 80 ? 'warning' : 'default'}
                                                        size="small"
                                                    />
                                                </td>
                                                <td style={{ padding: '12px 8px' }}>
                                                    <Typography variant="body2" sx={{ fontSize: '0.85rem' }}>
                                                        {pair.analysis.marketAnomaly}
                                                    </Typography>
                                                </td>
                                                <td style={{ padding: '12px 8px' }}>
                                                    <Typography variant="body2" sx={{ fontSize: '0.85rem' }}>
                                                        {pair.analysis.internalAnomaly}
                                                    </Typography>
                                                </td>
                                                <td style={{ padding: '12px 8px', textAlign: 'center' }}>
                                                    <Button
                                                        variant="outlined"
                                                        size="small"
                                                        onClick={() => openTradingView(pair.symbol)}
                                                        sx={{ fontSize: '0.75rem', minWidth: 'auto', px: 1 }}
                                                    >
                                                        TradingView
                                                    </Button>
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </Box>
                        </Box>
                    </Card>

                    {/* Trading Strategies Table */}
                    <Card sx={{ mb: 3, border: 1, borderColor: '#e0e0e0' }}>
                        <CardHeader
                            title="Trading Strategies"
                            action={
                                <IconButton
                                    onClick={handleExpandClick}
                                    aria-expanded={expanded}
                                    aria-label="show strategies"
                                    sx={{
                                        transform: expanded ? 'rotate(180deg)' : 'rotate(0deg)',
                                        transition: 'transform 0.3s',
                                    }}
                                >
                                    <ExpandMoreIcon />
                                </IconButton>
                            }
                            sx={{ background: '#f8f9fa', py: 1 }}
                        />
                        <Collapse in={expanded} timeout="auto" unmountOnExit>
                            <Box sx={{ p: 2 }}>
                                <Box sx={{ overflowX: 'auto' }}>
                                    <table style={{ width: '100%', borderCollapse: 'collapse' }}>
                                        <thead>
                                            <tr style={{ backgroundColor: '#f5f5f5' }}>
                                                <th style={{ padding: '12px 8px', textAlign: 'left', borderBottom: '2px solid #ddd', fontWeight: 'bold' }}>Symbol</th>
                                                <th style={{ padding: '12px 8px', textAlign: 'center', borderBottom: '2px solid #ddd', fontWeight: 'bold' }}>Direction</th>
                                                <th style={{ padding: '12px 8px', textAlign: 'left', borderBottom: '2px solid #ddd', fontWeight: 'bold' }}>Entry Levels</th>
                                                <th style={{ padding: '12px 8px', textAlign: 'left', borderBottom: '2px solid #ddd', fontWeight: 'bold' }}>Stop Loss</th>
                                                <th style={{ padding: '12px 8px', textAlign: 'left', borderBottom: '2px solid #ddd', fontWeight: 'bold' }}>Take Profit</th>
                                                <th style={{ padding: '12px 8px', textAlign: 'center', borderBottom: '2px solid #ddd', fontWeight: 'bold' }}>Duration</th>
                                                <th style={{ padding: '12px 8px', textAlign: 'center', borderBottom: '2px solid #ddd', fontWeight: 'bold' }}>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {data.strategies.map((strategy, index) => (
                                                <tr key={index} style={{ borderBottom: '1px solid #eee' }}>
                                                    <td style={{ padding: '12px 8px' }}>
                                                        <Typography
                                                            variant="body2"
                                                            sx={{
                                                                fontWeight: 'bold',
                                                                color: '#1976d2',
                                                                cursor: 'pointer',
                                                                '&:hover': { textDecoration: 'underline' }
                                                            }}
                                                            onDoubleClick={() => openTradingView(strategy.symbol)}
                                                        >
                                                            {strategy.symbol}
                                                        </Typography>
                                                    </td>
                                                    <td style={{ padding: '12px 8px', textAlign: 'center' }}>
                                                        <Chip
                                                            label={strategy.direction}
                                                            color={strategy.direction === 'Long' ? 'success' : 'error'}
                                                            size="small"
                                                        />
                                                    </td>
                                                    <td style={{ padding: '12px 8px' }}>
                                                        {strategy.entryLevels.map((entry, idx) => (
                                                            <Typography key={idx} variant="body2" sx={{ fontSize: '0.8rem', mb: 0.5 }}>
                                                                <strong>{entry.type}:</strong> {entry.level}
                                                            </Typography>
                                                        ))}
                                                    </td>
                                                    <td style={{ padding: '12px 8px' }}>
                                                        <Typography variant="body2" sx={{ fontSize: '0.8rem', fontWeight: 'bold', color: '#d32f2f' }}>
                                                            {strategy.riskManagement.stopLoss.level}
                                                        </Typography>
                                                    </td>
                                                    <td style={{ padding: '12px 8px' }}>
                                                        {strategy.takeProfitLevels.map((tp, idx) => (
                                                            <Typography key={idx} variant="body2" sx={{ fontSize: '0.8rem', mb: 0.5 }}>
                                                                <strong>{tp.level}</strong> (R:R {tp.riskRewardRatio})
                                                            </Typography>
                                                        ))}
                                                    </td>
                                                    <td style={{ padding: '12px 8px', textAlign: 'center' }}>
                                                        <Typography variant="body2" sx={{ fontSize: '0.8rem' }}>
                                                            {strategy.timeframe.tradeDuration}m
                                                        </Typography>
                                                    </td>
                                                    <td style={{ padding: '12px 8px', textAlign: 'center' }}>
                                                        <Button
                                                            variant="outlined"
                                                            size="small"
                                                            onClick={() => openTradingView(strategy.symbol)}
                                                            sx={{ fontSize: '0.75rem', minWidth: 'auto', px: 1 }}
                                                        >
                                                            TradingView
                                                        </Button>
                                                    </td>
                                                </tr>
                                            ))}
                                        </tbody>
                                    </table>
                                </Box>
                            </Box>
                        </Collapse>
                    </Card>

                    {/* Analysis Timestamp */}
                    {data.analysisTimestamp && (
                        <Typography variant="caption" color="textSecondary" sx={{ mt: 2, display: 'block' }}>
                            Analysis Timestamp: {new Date(data.analysisTimestamp).toLocaleString()}
                        </Typography>
                    )}
                </Box>
            );
        }

        // If it's a JSON object but not the expected structure, format it nicely
        if (typeof data === 'object') {
            return (
                <Box sx={{ fontFamily: 'monospace', fontSize: '0.9rem', lineHeight: 1.5 }}>
                    <pre style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-word' }}>
                        {JSON.stringify(data, null, 2)}
                    </pre>
                </Box>
            );
        }

        // For any other case, return as is
        return <Typography sx={{ whiteSpace: 'pre-wrap' }}>{String(data)}</Typography>;
    };

    return (
        <Card sx={{ mt: 2, mb: 2, border: 1, mx: 2, borderColor: '#ccc' }}>
            <CardHeader
                title="Cloud LLM Response"
                sx={{
                    background: '#f5f5f5',
                    borderRadius: 0,
                    padding: 1,
                    mb: 0
                }}
            />
            <Box sx={{ p: 2, minHeight: 100 }}>
                {formatAIResponse(props.aiResponse)}
            </Box>
        </Card>
    );
};
